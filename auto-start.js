#!/usr/bin/env node

// Script de démarrage automatique pour GestiCom
const { spawn, exec } = require('child_process');
const path = require('path');

console.log('\n========================================');
console.log('    GESTICOM - DÉMARRAGE AUTOMATIQUE');
console.log('========================================\n');

// Configuration
const PORT = 3000;
const SERVER_FILE = 'simple-server.js';
const APP_URL = `http://localhost:${PORT}/login.html`;

// Fonction pour ouvrir l'URL dans le navigateur
function openBrowser(url) {
  const platform = process.platform;
  let command;

  switch (platform) {
    case 'win32':
      command = `start ${url}`;
      break;
    case 'darwin':
      command = `open ${url}`;
      break;
    case 'linux':
      command = `xdg-open ${url}`;
      break;
    default:
      console.log(`⚠️  Impossible d'ouvrir automatiquement le navigateur sur ${platform}`);
      console.log(`🌐 Ouvrez manuellement: ${url}`);
      return;
  }

  exec(command, (error) => {
    if (error) {
      console.log(`⚠️  Erreur lors de l'ouverture du navigateur: ${error.message}`);
      console.log(`🌐 Ouvrez manuellement: ${url}`);
    } else {
      console.log('✅ Application ouverte dans le navigateur');
    }
  });
}

// Fonction pour vérifier si le serveur répond
function checkServer(callback) {
  const http = require('http');
  
  const options = {
    hostname: 'localhost',
    port: PORT,
    path: '/api/status',
    method: 'GET',
    timeout: 2000
  };

  const req = http.request(options, (res) => {
    callback(res.statusCode === 200);
  });

  req.on('error', () => {
    callback(false);
  });

  req.on('timeout', () => {
    req.destroy();
    callback(false);
  });

  req.end();
}

// Fonction principale
function startApplication() {
  console.log('🔍 Vérification de Node.js...');
  
  // Vérifier si le fichier serveur existe
  const fs = require('fs');
  if (!fs.existsSync(SERVER_FILE)) {
    console.log(`❌ Fichier ${SERVER_FILE} non trouvé`);
    console.log(`📁 Répertoire courant: ${process.cwd()}`);
    process.exit(1);
  }

  console.log('✅ Fichiers de l\'application trouvés');
  console.log('🚀 Démarrage du serveur GestiCom...');

  // Démarrer le serveur
  const server = spawn('node', [SERVER_FILE], {
    stdio: ['inherit', 'pipe', 'pipe'],
    cwd: process.cwd()
  });

  // Gérer la sortie du serveur
  server.stdout.on('data', (data) => {
    process.stdout.write(data);
  });

  server.stderr.on('data', (data) => {
    process.stderr.write(data);
  });

  // Gérer l'arrêt du serveur
  server.on('close', (code) => {
    console.log(`\n🛑 Serveur arrêté avec le code: ${code}`);
    process.exit(code);
  });

  server.on('error', (error) => {
    console.log(`❌ Erreur lors du démarrage du serveur: ${error.message}`);
    process.exit(1);
  });

  // Attendre que le serveur soit prêt
  console.log('⏳ Attente du démarrage du serveur...');
  
  let attempts = 0;
  const maxAttempts = 15;
  
  const checkInterval = setInterval(() => {
    attempts++;
    
    checkServer((isReady) => {
      if (isReady) {
        clearInterval(checkInterval);
        console.log('✅ Serveur démarré avec succès!');
        console.log('\n========================================');
        console.log('   GESTICOM EST MAINTENANT ACTIF !');
        console.log('========================================\n');
        console.log(`🌐 URL: ${APP_URL}`);
        console.log(`📊 API Status: http://localhost:${PORT}/api/status`);
        console.log('\n🔐 Comptes de démonstration:');
        console.log('• admin / admin (Administrateur)');
        console.log('• demo / demo (Démonstration)');
        console.log('\n💡 Pour arrêter le serveur, appuyez sur Ctrl+C\n');
        
        // Ouvrir le navigateur après un délai
        setTimeout(() => {
          openBrowser(APP_URL);
        }, 1000);
        
      } else if (attempts >= maxAttempts) {
        clearInterval(checkInterval);
        console.log(`❌ Échec du démarrage du serveur après ${maxAttempts} tentatives`);
        console.log(`🌐 Essayez d'ouvrir manuellement: ${APP_URL}`);
      } else if (attempts % 3 === 0) {
        console.log(`⏳ Tentative ${attempts}/${maxAttempts}...`);
      }
    });
  }, 1000);

  // Gérer l'arrêt propre
  process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt du serveur...');
    server.kill('SIGINT');
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Arrêt du serveur...');
    server.kill('SIGTERM');
  });
}

// Démarrer l'application
startApplication();
