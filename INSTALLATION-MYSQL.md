# 🗄️ Installation et Configuration de MySQL pour GestiCom

Ce guide vous explique comment installer et configurer MySQL pour utiliser GestiCom avec une vraie base de données.

## 📋 Prérequis

- Windows 10/11
- Droits d'administrateur
- Connexion Internet

## 🚀 Installation de MySQL

### Option 1: MySQL Installer (Recommandé)

1. **Téléchargez MySQL Installer**
   - Allez sur: https://dev.mysql.com/downloads/installer/
   - Choisissez "mysql-installer-web-community-8.x.x.msi"
   - Cliquez sur "Download"

2. **Exécutez l'installateur**
   - Double-cliquez sur le fichier téléchargé
   - Acceptez les termes de licence
   - Choisissez "Developer Default" ou "Server only"

3. **Configuration du serveur**
   - Type de configuration: "Development Computer"
   - Port: 3306 (par défaut)
   - Méthode d'authentification: "Use Strong Password Encryption"

4. **Définissez le mot de passe root**
   - Choisissez un mot de passe fort pour l'utilisateur "root"
   - **IMPORTANT**: Notez ce mot de passe !

5. **Terminez l'installation**
   - Cliquez sur "Execute" pour appliquer la configuration
   - Testez la connexion
   - Terminez l'installation

### Option 2: XAMPP (Plus simple)

1. **Téléchargez XAMPP**
   - Allez sur: https://www.apachefriends.org/
   - Téléchargez la version Windows

2. **Installez XAMPP**
   - Exécutez l'installateur
   - Sélectionnez au minimum: Apache, MySQL, PHP, phpMyAdmin

3. **Démarrez MySQL**
   - Ouvrez le panneau de contrôle XAMPP
   - Cliquez sur "Start" à côté de MySQL

## ⚙️ Configuration pour GestiCom

### 1. Modifiez le fichier de configuration

Ouvrez le fichier `config/mysql.js` et modifiez les paramètres :

```javascript
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'VOTRE_MOT_DE_PASSE', // Remplacez par votre mot de passe MySQL
  database: 'gesticom',
  port: 3306,
  charset: 'utf8mb4'
};
```

### 2. Testez la connexion

Ouvrez un terminal dans le dossier GestiCom et exécutez :

```bash
node setup-database.js
```

Si tout fonctionne, vous devriez voir :
```
✅ Connexion MySQL établie
✅ Base de données "gesticom" créée/vérifiée
✅ Données de démonstration insérées
🎉 Base de données GestiCom initialisée avec succès !
```

## 🚀 Démarrage avec Base de Données

### Méthode 1: Script automatique
Double-cliquez sur `DEMARRER-AVEC-BDD.bat`

### Méthode 2: Ligne de commande
```bash
node start-with-db.js
```

### Méthode 3: Initialisation manuelle
```bash
# 1. Initialiser la base de données
node setup-database.js

# 2. Démarrer le serveur
node simple-server.js
```

## 🔧 Dépannage

### Erreur: "Access denied for user 'root'"
- Vérifiez le mot de passe dans `config/mysql.js`
- Assurez-vous que MySQL est démarré

### Erreur: "connect ECONNREFUSED"
- Vérifiez que MySQL est en cours d'exécution
- Vérifiez le port (3306 par défaut)

### Erreur: "ER_NOT_SUPPORTED_AUTH_MODE"
- Utilisez MySQL 8.0+ avec l'authentification legacy
- Ou modifiez la méthode d'authentification

### MySQL ne démarre pas
- Vérifiez que le port 3306 n'est pas utilisé
- Redémarrez en tant qu'administrateur
- Vérifiez les logs MySQL

## 📊 Vérification de l'Installation

### 1. Via l'application
- Démarrez GestiCom
- Allez sur: http://localhost:3000/api/status
- Vérifiez que `"mysql": true`

### 2. Via phpMyAdmin (si XAMPP)
- Ouvrez: http://localhost/phpmyadmin
- Vérifiez que la base "gesticom" existe
- Vérifiez les tables: products, clients, suppliers, etc.

### 3. Via ligne de commande MySQL
```bash
mysql -u root -p
USE gesticom;
SHOW TABLES;
SELECT COUNT(*) FROM products;
```

## 🎯 Avantages de MySQL

Avec MySQL configuré, GestiCom offre :

- ✅ **Persistance des données** - Les données survivent aux redémarrages
- ✅ **Performance** - Requêtes optimisées et indexées
- ✅ **Sécurité** - Authentification et chiffrement
- ✅ **Évolutivité** - Support de grandes quantités de données
- ✅ **Sauvegarde** - Possibilité de sauvegarder/restaurer
- ✅ **Multi-utilisateurs** - Accès concurrent sécurisé

## 📞 Support

Si vous rencontrez des problèmes :

1. **Vérifiez les logs** dans le terminal
2. **Consultez la documentation** MySQL officielle
3. **Utilisez le mode fallback** en cas de problème (les données de test restent disponibles)

## 🔄 Retour aux Données de Test

Si vous voulez revenir aux données de test sans MySQL :

1. Arrêtez MySQL
2. Utilisez `DEMARRER.bat` au lieu de `DEMARRER-AVEC-BDD.bat`
3. L'application utilisera automatiquement les données de fallback

---

**Note**: GestiCom fonctionne parfaitement avec ou sans MySQL. La base de données améliore les fonctionnalités mais n'est pas obligatoire pour tester l'application.
