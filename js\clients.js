const clientManager = {
  getAll: async () => {
    try {
      // Essayer d'appeler l'API
      const response = await fetch('/api/clients', {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('authToken')}` }
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, utilisation des données de test");
      // Retourner des données de test si l'API échoue
      return [
        { id: 1, name: "Dupont Entreprise", email: "<EMAIL>", phone: "01 23 45 67 89", address: "123 Rue de Paris, 75001 Paris" },
        { id: 2, name: "<PERSON>", email: "<EMAIL>", phone: "01 98 76 54 32", address: "45 Avenue des Champs, 75008 Paris" },
        { id: 3, name: "Petit Commerce", email: "<EMAIL>", phone: "01 45 67 89 10", address: "78 Boulevard Haussmann, 75009 Paris" }
      ];
    }
  },
  getById: async (id) => {
    try {
      const response = await fetch(`/api/clients/${id}`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('authToken')}` }
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de récupération");
      // Simuler la récupération d'un client
      const testClients = [
        { id: 1, name: "Dupont Entreprise", contactPerson: "Jean Dupont", email: "<EMAIL>", phone: "01 23 45 67 89", address: "123 Rue de Paris", city: "Paris", postalCode: "75001", country: "France", taxId: "FR123456789", notes: "" },
        { id: 2, name: "Martin SARL", contactPerson: "Marie Martin", email: "<EMAIL>", phone: "01 98 76 54 32", address: "45 Avenue des Champs", city: "Paris", postalCode: "75008", country: "France", taxId: "FR987654321", notes: "" },
        { id: 3, name: "Petit Commerce", contactPerson: "Pierre Petit", email: "<EMAIL>", phone: "01 45 67 89 10", address: "78 Boulevard Haussmann", city: "Paris", postalCode: "75009", country: "France", taxId: "FR456789123", notes: "" }
      ];
      return testClients.find(c => c.id === parseInt(id)) || null;
    }
  },
  add: async (client) => {
    try {
      const response = await fetch('/api/clients', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(client)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation d'ajout");
      // Simuler un ajout réussi
      return { ...client, id: Math.floor(Math.random() * 1000) + 10 };
    }
  },
  update: async (id, data) => {
    try {
      const response = await fetch(`/api/clients/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(data)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de mise à jour");
      // Simuler une mise à jour réussie
      return { ...data, id: parseInt(id) };
    }
  },
  delete: async (id) => {
    try {
      await fetch(`/api/clients/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${localStorage.getItem('authToken')}` }
      });
      return true;
    } catch (error) {
      console.log("API non disponible, simulation de suppression");
      // Simuler une suppression réussie
      return true;
    }
  }
};

// Fonction d'initialisation pour la section clients
function initClientsSection() {
  // Charger la liste des clients
  loadClientsList();

  // Gestionnaire pour le bouton d'enregistrement de client
  document.getElementById('saveClientBtn').addEventListener('click', saveClient);

  // Gestionnaire pour la recherche
  document.getElementById('searchClient').addEventListener('input', filterClients);
}

// Fonction pour charger la liste des clients
async function loadClientsList() {
  try {
    const clients = await clientManager.getAll();
    const tableBody = document.getElementById('clientsTableBody');

    if (clients.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="6" class="text-center">Aucun client trouvé</td></tr>';
      return;
    }

    tableBody.innerHTML = '';
    clients.forEach(client => {
      tableBody.innerHTML += `
        <tr>
          <td>${client.id}</td>
          <td>${client.name}</td>
          <td>${client.email}</td>
          <td>${client.phone || '-'}</td>
          <td>${client.address || '-'}</td>
          <td>
            <button class="btn btn-sm btn-outline-primary edit-client" data-id="${client.id}">
              <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-client" data-id="${client.id}">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        </tr>
      `;
    });

    // Ajouter les gestionnaires d'événements pour les boutons d'édition et de suppression
    addClientActionHandlers();
  } catch (error) {
    console.error('Erreur lors du chargement des clients:', error);
    document.getElementById('clientsTableBody').innerHTML =
      '<tr><td colspan="6" class="text-center text-danger">Erreur lors du chargement des clients</td></tr>';
  }
}

// Fonction pour sauvegarder un client (nouveau ou mise à jour)
async function saveClient() {
  const clientId = document.getElementById('clientId')?.value;

  // Récupérer les valeurs du formulaire
  const name = document.getElementById('clientName').value.trim();
  const email = document.getElementById('clientEmail').value.trim();
  const phone = document.getElementById('clientPhone').value.trim();
  const address = document.getElementById('clientAddress').value.trim();

  // Validation basique
  if (!name) {
    showError('Le nom du client est obligatoire');
    return;
  }

  if (!email) {
    showError('L\'email du client est obligatoire');
    return;
  }

  const client = {
    name: name,
    contactPerson: '', // Champ vide pour l'instant
    email: email,
    phone: phone || null,
    address: address || null,
    city: null, // Champ vide pour l'instant
    postalCode: null, // Champ vide pour l'instant
    country: 'Algérie', // Valeur par défaut
    taxId: null, // Champ vide pour l'instant
    notes: null // Champ vide pour l'instant
  };

  console.log('Données à envoyer:', client);

  try {
    let result;
    if (clientId) {
      // Mise à jour d'un client existant
      console.log(`Mise à jour du client ${clientId}`);
      result = await clientManager.update(clientId, client);
    } else {
      // Ajout d'un nouveau client
      console.log('Ajout d\'un nouveau client');
      result = await clientManager.add(client);
    }

    console.log('Résultat:', result);

    // Fermer le modal et rafraîchir la liste
    const modal = bootstrap.Modal.getInstance(document.getElementById('addClientModal'));
    modal.hide();

    // Réinitialiser le formulaire
    document.getElementById('addClientForm').reset();
    document.getElementById('clientId').value = '';
    document.getElementById('addClientModalLabel').textContent = 'Ajouter un client';

    // Rafraîchir la liste
    loadClientsList();

    // Afficher un message de succès
    showSuccess(clientId ? 'Client modifié avec succès' : 'Client ajouté avec succès');

  } catch (error) {
    console.error('Erreur lors de l\'enregistrement du client:', error);
    showError('Erreur lors de l\'enregistrement du client: ' + error.message);
  }
}

// Fonction pour filtrer les clients selon la recherche
function filterClients() {
  const searchTerm = document.getElementById('searchClient').value.toLowerCase();
  const rows = document.querySelectorAll('#clientsTableBody tr');

  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm) ? '' : 'none';
  });
}

// Fonction pour ajouter les gestionnaires d'événements aux boutons d'action
function addClientActionHandlers() {
  // Gestionnaires pour les boutons d'édition
  document.querySelectorAll('.edit-client').forEach(button => {
    button.addEventListener('click', async (e) => {
      const clientId = e.currentTarget.getAttribute('data-id');
      try {
        console.log(`Édition du client ${clientId}`);
        const client = await clientManager.getById(clientId);

        if (!client) {
          showError('Client non trouvé');
          return;
        }

        // Remplir le formulaire avec les données du client
        document.getElementById('clientId').value = client.id;
        document.getElementById('clientName').value = client.name || '';
        document.getElementById('clientEmail').value = client.email || '';
        document.getElementById('clientPhone').value = client.phone || '';
        document.getElementById('clientAddress').value = client.address || '';

        // Changer le titre du modal
        document.getElementById('addClientModalLabel').textContent = 'Modifier un client';

        // Ouvrir le modal
        const modal = new bootstrap.Modal(document.getElementById('addClientModal'));
        modal.show();
      } catch (error) {
        console.error('Erreur lors de la récupération du client:', error);
        showError('Erreur lors de la récupération du client: ' + error.message);
      }
    });
  });

  // Gestionnaires pour les boutons de suppression
  document.querySelectorAll('.delete-client').forEach(button => {
    button.addEventListener('click', async (e) => {
      const clientId = e.currentTarget.getAttribute('data-id');
      const clientName = e.currentTarget.closest('tr').querySelector('td:nth-child(2)').textContent;
      const confirmed = await showConfirm(
        `Êtes-vous sûr de vouloir supprimer le client "${clientName}" ?`,
        'Supprimer le client',
        { confirmText: 'Supprimer', cancelText: 'Annuler' }
      );

      if (confirmed) {
        try {
          console.log(`Suppression du client ${clientId}`);
          await clientManager.delete(clientId);
          loadClientsList();
          showSuccess('Client supprimé avec succès');
        } catch (error) {
          console.error('Erreur lors de la suppression du client:', error);
          showError('Erreur lors de la suppression du client: ' + error.message);
        }
      }
    });
  });
}


