// ===== GESTICOM - APPLICATION PRINCIPALE =====

document.addEventListener('DOMContentLoaded', function() {
  console.log('🚀 Initialisation de GestiCom...');
  
  // Initialiser l'application
  initializeApp();
  
  // Configurer la navigation
  setupNavigation();
  
  // Configurer les événements
  setupEventListeners();
  
  // Charger la section par défaut
  loadSection('dashboard');
  
  console.log('✅ GestiCom initialisé avec succès');
});

// ===== INITIALISATION =====
function initializeApp() {
  // Vérifier l'authentification
  if (!isAuthenticated()) {
    redirectToLogin();
    return;
  }
  
  // Initialiser les composants
  initializeComponents();
}

function initializeComponents() {
  // Initialiser les tooltips Bootstrap
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });
  
  // Initialiser les popovers Bootstrap
  const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
  popoverTriggerList.map(function (popoverTriggerEl) {
    return new bootstrap.Popover(popoverTriggerEl);
  });
}

// ===== NAVIGATION =====
function setupNavigation() {
  const navLinks = document.querySelectorAll('.nav-link[data-section]');
  
  navLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const section = this.getAttribute('data-section');
      loadSection(section);
      setActiveNavLink(this);
    });
  });
}

function setActiveNavLink(activeLink) {
  // Retirer la classe active de tous les liens
  document.querySelectorAll('.nav-link').forEach(link => {
    link.classList.remove('active');
  });
  
  // Ajouter la classe active au lien cliqué
  activeLink.classList.add('active');
}

function loadSection(section) {
  console.log(`📄 Chargement de la section: ${section}`);
  
  // Mettre à jour le titre
  updateSectionTitle(section);
  
  // Charger le contenu de la section
  loadSectionContent(section);
  
  // Initialiser la section
  initializeSection(section);
}

function updateSectionTitle(section) {
  const titles = {
    'dashboard': 'Tableau de bord',
    'products': 'Gestion des Produits',
    'clients': 'Gestion des Clients',
    'suppliers': 'Gestion des Fournisseurs',
    'pos': 'Point de Vente',
    'quotes': 'Gestion des Devis',
    'purchase-orders': 'Bons de Commande',
    'invoices': 'Gestion des Factures',
    'delivery': 'Bons de Livraison',
    'inventory': 'Gestion de l\'Inventaire',
    'sales-returns': 'Retours Clients',
    'purchase-returns': 'Retours Fournisseurs',
    'settings': 'Paramètres',
    'super-admin': 'Administration'
  };
  
  const titleElement = document.getElementById('section-title');
  if (titleElement) {
    titleElement.textContent = titles[section] || 'Section inconnue';
  }
}

async function loadSectionContent(section) {
  const container = document.getElementById('app-container');
  
  if (!container) {
    console.error('❌ Container app-container non trouvé');
    return;
  }
  
  try {
    // Afficher un indicateur de chargement
    container.innerHTML = `
      <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Chargement...</span>
        </div>
        <p class="mt-3 text-muted">Chargement de ${section}...</p>
      </div>
    `;
    
    // Charger le contenu HTML de la section
    const response = await fetch(`sections/${section}.html`);
    
    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }
    
    const html = await response.text();
    container.innerHTML = html;
    
    // Ajouter l'animation de fade-in
    container.classList.add('fade-in');
    
    console.log(`✅ Section ${section} chargée avec succès`);
    
  } catch (error) {
    console.error(`❌ Erreur lors du chargement de la section ${section}:`, error);
    container.innerHTML = `
      <div class="alert alert-danger" role="alert">
        <h4 class="alert-heading">Erreur de chargement</h4>
        <p>Impossible de charger la section "${section}".</p>
        <hr>
        <p class="mb-0">Veuillez vérifier votre connexion et réessayer.</p>
      </div>
    `;
  }
}

function initializeSection(section) {
  try {
    switch(section) {
      case 'dashboard':
        initDashboard();
        break;
      case 'products':
        if (typeof loadProductsList === 'function') loadProductsList();
        break;
      case 'clients':
        if (typeof loadClientsList === 'function') loadClientsList();
        break;
      case 'suppliers':
        if (typeof loadSuppliersList === 'function') loadSuppliersList();
        break;
      case 'pos':
        if (typeof initPOS === 'function') initPOS();
        break;
      case 'quotes':
        if (typeof loadQuotesList === 'function') loadQuotesList();
        break;
      case 'invoices':
        if (typeof loadInvoicesList === 'function') loadInvoicesList();
        break;
      case 'inventory':
        if (typeof loadInventory === 'function') loadInventory();
        break;
      default:
        console.log(`ℹ️ Aucune initialisation spécifique pour la section: ${section}`);
    }
  } catch (error) {
    console.error(`❌ Erreur lors de l'initialisation de la section ${section}:`, error);
  }
}

// ===== TABLEAU DE BORD =====
async function initDashboard() {
  console.log('📊 Initialisation du tableau de bord...');
  
  try {
    // Charger les données du tableau de bord
    await loadDashboardData();
    console.log('✅ Tableau de bord initialisé');
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation du tableau de bord:', error);
  }
}

async function loadDashboardData() {
  try {
    // Récupérer les produits pour les statistiques
    const products = await productManager.getAll();
    
    // Calculer les statistiques
    const stats = calculateStats(products);
    
    // Mettre à jour l'interface
    updateDashboardStats(stats);
    updateLowStockList(products);
    
  } catch (error) {
    console.error('❌ Erreur lors du chargement des données du tableau de bord:', error);
    showDashboardError();
  }
}

function calculateStats(products) {
  return {
    totalProducts: products.length,
    totalStock: products.reduce((sum, product) => sum + product.stockQuantity, 0),
    lowStockProducts: products.filter(product => product.stockQuantity < 10).length,
    outOfStockProducts: products.filter(product => product.stockQuantity === 0).length,
    stockValue: products.reduce((sum, product) => sum + (product.price * product.stockQuantity), 0)
  };
}

function updateDashboardStats(stats) {
  const elements = {
    'total-products': stats.totalProducts,
    'total-stock': stats.totalStock,
    'low-stock': stats.lowStockProducts,
    'out-of-stock': stats.outOfStockProducts,
    'stock-value': `${stats.stockValue.toFixed(2)} DA`
  };
  
  Object.entries(elements).forEach(([id, value]) => {
    const element = document.getElementById(id);
    if (element) {
      element.textContent = value;
    }
  });
}

function updateLowStockList(products) {
  const lowStockList = document.getElementById('low-stock-list');
  if (!lowStockList) return;
  
  const lowStockItems = products.filter(product => product.stockQuantity < 10);
  
  if (lowStockItems.length === 0) {
    lowStockList.innerHTML = '<li class="list-group-item">Aucun produit en stock faible</li>';
  } else {
    lowStockList.innerHTML = lowStockItems.map(product => `
      <li class="list-group-item d-flex justify-content-between align-items-center">
        ${product.name}
        <span class="badge bg-warning rounded-pill">${product.stockQuantity}</span>
      </li>
    `).join('');
  }
}

function showDashboardError() {
  const container = document.getElementById('dashboard-container');
  if (container) {
    container.innerHTML = `
      <div class="alert alert-danger" role="alert">
        <h4 class="alert-heading">Erreur</h4>
        <p>Impossible de charger les données du tableau de bord.</p>
      </div>
    `;
  }
}

// ===== ÉVÉNEMENTS =====
function setupEventListeners() {
  // Bouton de déconnexion
  const logoutBtn = document.getElementById('logout');
  if (logoutBtn) {
    logoutBtn.addEventListener('click', function(e) {
      e.preventDefault();
      logout();
    });
  }
  
  // Toggle sidebar pour mobile
  const sidebarToggle = document.getElementById('sidebar-toggle');
  if (sidebarToggle) {
    sidebarToggle.addEventListener('click', function() {
      const sidebar = document.getElementById('sidebar');
      sidebar.classList.toggle('show');
    });
  }
}

// ===== UTILITAIRES =====
function isAuthenticated() {
  return localStorage.getItem('authToken') !== null;
}

function redirectToLogin() {
  window.location.replace('login.html');
}

function logout() {
  localStorage.removeItem('authToken');
  redirectToLogin();
}

// ===== GESTION DES ERREURS =====
window.addEventListener('error', function(event) {
  console.error('❌ Erreur JavaScript:', event.error);
});

window.addEventListener('unhandledrejection', function(event) {
  console.error('❌ Promise rejetée:', event.reason);
});

console.log('📄 Module principal chargé');
