# GestiCom - Script de démarrage automatique PowerShell
param(
    [switch]$NoOpen,
    [int]$Port = 3000
)

# Configuration des couleurs
$Host.UI.RawUI.WindowTitle = "GestiCom - Démarrage Automatique"

function Write-Step {
    param([string]$Message, [string]$Status = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-Host "[$timestamp] ✓ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ✗ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] ⚠ $Message" -ForegroundColor Yellow }
        default   { Write-Host "[$timestamp] ℹ $Message" -ForegroundColor Cyan }
    }
}

function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

function Stop-ExistingServer {
    param([int]$Port)
    Write-Step "Vérification des processus existants sur le port $Port..."

    # Arrêter les processus Node.js existants
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($nodeProcesses) {
        Write-Step "Arrêt des processus Node.js existants..." "WARNING"
        $nodeProcesses | Stop-Process -Force -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 2
    }

    # Vérifier si le port est libre
    if (Test-Port -Port $Port) {
        Write-Step "Le port $Port est encore occupé, tentative de libération..." "WARNING"
        try {
            $netstat = netstat -ano | Select-String ":$Port "
            if ($netstat) {
                $processId = ($netstat -split '\s+')[-1]
                Stop-Process -Id $processId -Force -ErrorAction SilentlyContinue
                Start-Sleep -Seconds 1
            }
        }
        catch {
            Write-Step "Impossible de libérer le port automatiquement" "WARNING"
        }
    }
}

# Bannière de démarrage
Clear-Host
Write-Host ""
Write-Host "========================================" -ForegroundColor Magenta
Write-Host "    GESTICOM - DÉMARRAGE AUTOMATIQUE   " -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Magenta
Write-Host ""

try {
    # Étape 1: Vérification de Node.js
    Write-Step "Vérification de Node.js..."
    try {
        $nodeVersion = node --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Step "Node.js détecté: $nodeVersion" "SUCCESS"
        } else {
            throw "Node.js non trouvé"
        }
    }
    catch {
        Write-Step "Node.js n'est pas installe ou non trouve dans le PATH" "ERROR"
        Write-Step "Veuillez installer Node.js depuis https://nodejs.org/" "ERROR"
        Read-Host "Appuyez sur Entree pour quitter"
        exit 1
    }

    # Étape 2: Vérification du répertoire
    Write-Step "Vérification du répertoire de travail..."
    if (-not (Test-Path "simple-server.js")) {
        Write-Step "Fichier simple-server.js non trouvé dans le répertoire courant" "ERROR"
        Write-Step "Repertoire courant: $(Get-Location)" "ERROR"
        Read-Host "Appuyez sur Entree pour quitter"
        exit 1
    }
    Write-Step "Fichiers de l'application trouvés" "SUCCESS"

    # Étape 3: Vérification des dépendances
    Write-Step "Vérification des dépendances..."
    if (-not (Test-Path "node_modules")) {
        Write-Step "Installation des dépendances npm..."
        npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Step "Echec de l'installation des dependances" "ERROR"
            Read-Host "Appuyez sur Entree pour quitter"
            exit 1
        }
    }
    Write-Step "Dépendances OK" "SUCCESS"

    # Étape 4: Arrêt des serveurs existants
    Stop-ExistingServer -Port $Port

    # Étape 5: Démarrage du serveur
    Write-Step "Démarrage du serveur GestiCom sur le port $Port..."

    # Démarrer le serveur en arrière-plan
    $serverJob = Start-Job -ScriptBlock {
        param($ServerPath, $Port)
        Set-Location $ServerPath
        node simple-server.js
    } -ArgumentList (Get-Location), $Port

    # Attendre que le serveur soit prêt
    Write-Step "Attente du démarrage du serveur..."
    $maxAttempts = 15
    $attempt = 0
    $serverReady = $false

    while ($attempt -lt $maxAttempts -and -not $serverReady) {
        Start-Sleep -Seconds 1
        $attempt++
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$Port/api/status" -TimeoutSec 2 -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                $serverReady = $true
            }
        }
        catch {
            # Continuer à attendre
        }

        if ($attempt % 3 -eq 0) {
            Write-Step "Tentative $attempt/$maxAttempts..."
        }
    }

    if ($serverReady) {
        Write-Step "Serveur démarré avec succès!" "SUCCESS"

        # Étape 6: Ouverture de l'application
        if (-not $NoOpen) {
            Write-Step "Ouverture de l'application dans le navigateur..."
            Start-Process "http://localhost:$Port/login.html"
            Write-Step "Application ouverte dans le navigateur" "SUCCESS"
        }

        # Affichage des informations
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "   GESTICOM EST MAINTENANT ACTIF !     " -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        Write-Host "✓ Serveur: http://localhost:$Port" -ForegroundColor Green
        Write-Host "✓ Application: http://localhost:$Port/login.html" -ForegroundColor Green
        Write-Host "✓ API Status: http://localhost:$Port/api/status" -ForegroundColor Green
        Write-Host ""
        Write-Host "Comptes de démonstration:" -ForegroundColor Yellow
        Write-Host "• admin / admin (Administrateur)" -ForegroundColor Yellow
        Write-Host "• demo / demo (Démonstration)" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Pour arrêter le serveur, appuyez sur Ctrl+C ou fermez cette fenêtre" -ForegroundColor Cyan
        Write-Host ""

        # Garder le script en vie et afficher les logs
        Write-Step "Serveur en cours d'exécution... (Ctrl+C pour arrêter)"

        try {
            while ($true) {
                Start-Sleep -Seconds 5
                # Vérifier si le serveur est toujours actif
                if ($serverJob.State -ne "Running") {
                    Write-Step "Le serveur s'est arrêté de manière inattendue" "ERROR"
                    break
                }
            }
        }
        catch {
            Write-Step "Arrêt du serveur..." "WARNING"
        }
        finally {
            # Nettoyer les processus
            if ($serverJob) {
                Stop-Job $serverJob -ErrorAction SilentlyContinue
                Remove-Job $serverJob -ErrorAction SilentlyContinue
            }
            Stop-ExistingServer -Port $Port
            Write-Step "Serveur arrêté" "SUCCESS"
        }
    }
    else {
        Write-Step "Echec du demarrage du serveur apres $maxAttempts tentatives" "ERROR"
        if ($serverJob) {
            Stop-Job $serverJob -ErrorAction SilentlyContinue
            Remove-Job $serverJob -ErrorAction SilentlyContinue
        }
        exit 1
    }
}
catch {
    Write-Step "Erreur inattendue: $($_.Exception.Message)" "ERROR"
    Read-Host "Appuyez sur Entree pour quitter"
    exit 1
}
