@echo off
title GestiCom - Connecte a MySQL
color 0A

echo.
echo ==========================================
echo   GESTICOM - CONNECTE A MYSQL
echo ==========================================
echo.

echo Verification de la connexion MySQL...
node test-final.js

echo.
echo Demarrage du serveur avec MySQL...
echo.

REM Demarrer le serveur connecte a MySQL
start "Serveur GestiCom MySQL" /MIN node simple-server.js

REM Attendre 4 secondes
ping 127.0.0.1 -n 5 > nul

echo Ouverture de l'application...
start http://localhost:3000/login.html

echo.
echo ==========================================
echo   GESTICOM CONNECTE A VOTRE BASE MYSQL !
echo ==========================================
echo.
echo Application: http://localhost:3000/login.html
echo API Status: http://localhost:3000/api/status
echo.
echo Base de donnees: gesticom (MySQL)
echo Produits: 4 dans votre base
echo Clients: 3 dans votre base
echo Fournisseurs: 3 dans votre base
echo.
echo Comptes de connexion:
echo.
echo   admin / admin    (Administrateur)
echo.
echo L'application utilise maintenant VOS VRAIES DONNEES !
echo.
echo Pour arreter l'application:
echo - Fermez la fenetre "Serveur GestiCom MySQL"
echo - Ou fermez cette fenetre
echo.

pause
