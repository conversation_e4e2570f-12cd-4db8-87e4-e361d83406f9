@echo off
cls
echo.
echo ==========================================
echo    GESTICOM - LANCEMENT AUTOMATIQUE
echo ==========================================
echo.
echo Demarrage du serveur...
echo.

REM Demarrer le serveur en arriere-plan
start /B npm start

REM Attendre que le serveur demarre
echo Attente du demarrage du serveur...
timeout /t 4 /nobreak >nul

REM Ouvrir l'application dans le navigateur
echo Ouverture de l'application...
start http://localhost:3000/login.html

echo.
echo ==========================================
echo     GESTICOM EST MAINTENANT ACTIF !
echo ==========================================
echo.
echo URL: http://localhost:3000/login.html
echo.
echo Comptes de test:
echo - admin / admin
echo - demo / demo
echo.
echo Pour arreter, fermez cette fenetre.
echo.

REM Garder la fenetre ouverte
pause >nul
