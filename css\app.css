/* ===== GESTICOM - STYLES PRINCIPAUX ===== */

/* Variables CSS pour une cohérence visuelle */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  
  --sidebar-width: 250px;
  --header-height: 60px;
  --border-radius: 0.375rem;
  --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --transition: all 0.3s ease;
}

/* ===== LAYOUT GÉNÉRAL ===== */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  color: #212529;
  line-height: 1.6;
}

.container-fluid {
  padding: 0;
}

/* ===== SIDEBAR ===== */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: var(--sidebar-width);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  z-index: 1000;
  overflow-y: auto;
  transition: var(--transition);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar .position-sticky {
  padding: 1.5rem 0;
}

.sidebar h4 {
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.sidebar .nav-link {
  color: rgba(255, 255, 255, 0.9);
  padding: 0.75rem 1.5rem;
  margin: 0.25rem 0;
  border-radius: 0;
  transition: var(--transition);
  border-left: 3px solid transparent;
}

.sidebar .nav-link:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
  border-left-color: white;
  transform: translateX(5px);
}

.sidebar .nav-link.active {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
  border-left-color: white;
  font-weight: 600;
}

.sidebar .nav-link i {
  width: 20px;
  text-align: center;
}

/* ===== CONTENU PRINCIPAL ===== */
.main-content {
  margin-left: var(--sidebar-width);
  min-height: 100vh;
  padding: 2rem;
  transition: var(--transition);
}

.page-header {
  background: white;
  padding: 1.5rem 2rem;
  margin: -2rem -2rem 2rem -2rem;
  border-bottom: 1px solid #e9ecef;
  box-shadow: var(--box-shadow);
}

.page-header h1 {
  margin: 0;
  color: var(--dark-color);
  font-weight: 600;
}

/* ===== CARTES ===== */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  margin-bottom: 1.5rem;
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
  color: var(--dark-color);
}

/* ===== CARTES STATISTIQUES ===== */
.stats-card {
  text-align: center;
  padding: 2rem 1rem;
  border-radius: var(--border-radius);
  color: white;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
}

.stats-card .display-4 {
  font-weight: 700;
  margin: 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stats-card .card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Couleurs spécifiques pour les cartes statistiques */
.bg-primary { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important; }
.bg-success { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important; }
.bg-warning { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important; color: #212529 !important; }
.bg-danger { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important; }
.bg-info { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important; }

/* ===== TABLEAUX ===== */
.table {
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.table thead th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: var(--dark-color);
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

.table tbody tr {
  transition: var(--transition);
}

.table tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
  transform: scale(1.01);
}

/* ===== FORMULAIRES ===== */
.form-control, .form-select {
  border-radius: var(--border-radius);
  border: 1px solid #ced4da;
  transition: var(--transition);
  padding: 0.75rem 1rem;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  transform: translateY(-1px);
}

.form-label {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
}

/* ===== BOUTONS ===== */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: var(--transition);
  border: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.875rem;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.btn-danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.btn-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: #212529;
}

.btn-info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

/* ===== BADGES ===== */
.badge {
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius);
}

/* ===== ALERTES ===== */
.alert {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  border-left: 4px solid;
}

.alert-success { border-left-color: var(--success-color); }
.alert-danger { border-left-color: var(--danger-color); }
.alert-warning { border-left-color: var(--warning-color); }
.alert-info { border-left-color: var(--info-color); }

/* ===== MODALES ===== */
.modal-content {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
}

/* ===== UTILITAIRES ===== */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-custom {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.border-gradient {
  border: 2px solid;
  border-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%) 1;
}

/* ===== CORRECTIONS SPÉCIFIQUES ===== */
.list-group-item {
  border: 1px solid #dee2e6;
  background-color: white;
  color: #212529;
}

.list-group-item:hover {
  background-color: #f8f9fa;
}

/* Assurer la visibilité du texte */
h1, h2, h3, h4, h5, h6, p, span, div, td, th, li, label {
  color: inherit;
}

/* Styles pour les éléments du tableau de bord */
#stock-value {
  font-size: 2rem;
  font-weight: 700;
  color: #212529;
}

#low-stock-list {
  color: #212529;
}

#low-stock-list .list-group-item {
  background-color: white;
  color: #212529;
  border: 1px solid #dee2e6;
}
