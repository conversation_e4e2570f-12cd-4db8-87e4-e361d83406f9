/* ===== GESTICOM - STYLES ORIGINAUX ===== */

/* Reset et base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  color: #212529;
  line-height: 1.6;
}

/* Container principal */
.container-fluid {
  padding: 0;
}

/* Sidebar */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 250px;
  background-color: #343a40;
  color: white;
  z-index: 1000;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.sidebar .position-sticky {
  padding: 1rem 0;
}

.sidebar h4 {
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
  color: white;
  padding: 0 1rem;
}

.sidebar .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 0.75rem 1rem;
  margin: 0.25rem 0;
  border-radius: 0;
  transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
  color: white;
  background-color: #007bff;
  font-weight: 500;
}

.sidebar .nav-link i {
  width: 20px;
  text-align: center;
}

/* Contenu principal */
.main-content {
  margin-left: 250px;
  min-height: 100vh;
  padding: 1rem;
  transition: all 0.3s ease;
}

.page-header {
  background: white;
  padding: 1rem 1.5rem;
  margin: -1rem -1rem 1rem -1rem;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0;
  color: #343a40;
  font-weight: 500;
}

/* Cartes */
.card {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  margin-bottom: 1rem;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-weight: 500;
  color: #495057;
}

.card-body {
  padding: 1.25rem;
}

/* Cartes colorées */
.card.bg-primary {
  background-color: #007bff !important;
  color: white;
}

.card.bg-success {
  background-color: #28a745 !important;
  color: white;
}

.card.bg-warning {
  background-color: #ffc107 !important;
  color: #212529;
}

.card.bg-danger {
  background-color: #dc3545 !important;
  color: white;
}

.card.bg-info {
  background-color: #17a2b8 !important;
  color: white;
}

/* Tableaux */
.table {
  background: white;
  border-radius: 0.375rem;
  overflow: hidden;
}

.table thead th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 500;
  color: #495057;
}

.table tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

/* Formulaires */
.form-control, .form-select {
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
  padding: 0.375rem 0.75rem;
}

.form-control:focus, .form-select:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.5rem;
}

/* Boutons */
.btn {
  border-radius: 0.375rem;
  font-weight: 400;
  padding: 0.375rem 0.75rem;
  transition: all 0.15s ease-in-out;
}

.btn:hover {
  transform: translateY(-1px);
}

/* Badges */
.badge {
  font-weight: 500;
  padding: 0.25em 0.4em;
  border-radius: 0.25rem;
}

/* Alertes */
.alert {
  border: 1px solid transparent;
  border-radius: 0.375rem;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
}

/* Modales */
.modal-content {
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.375rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

/* Animations simples */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }
}

/* Corrections spécifiques */
.list-group-item {
  border: 1px solid #dee2e6;
  background-color: white;
  color: #212529;
  padding: 0.75rem 1.25rem;
}

.list-group-item:hover {
  background-color: #f8f9fa;
}

/* Styles pour les éléments du tableau de bord */
#stock-value {
  font-size: 2rem;
  font-weight: 600;
  color: #212529;
}

#low-stock-list {
  color: #212529;
}

#low-stock-list .list-group-item {
  background-color: white;
  color: #212529;
  border: 1px solid #dee2e6;
}

/* Assurer la visibilité du texte */
h1, h2, h3, h4, h5, h6, p, span, div, td, th, li, label {
  color: inherit;
}

/* Styles pour les cartes du tableau de bord */
.stats-card {
  text-align: center;
  padding: 1.5rem 1rem;
}

.stats-card .display-4 {
  font-weight: 600;
  margin: 0.5rem 0;
}

.stats-card .card-title {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

/* Ombres personnalisées */
.shadow-custom {
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1) !important;
}
