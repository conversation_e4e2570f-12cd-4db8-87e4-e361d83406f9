// Script pour lister toutes les bases de données MySQL disponibles
const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '', // Modifiez selon votre configuration MySQL
  port: 3306
  // Pas de database spécifiée pour lister toutes les bases
};

console.log('🔍 Recherche des bases de données MySQL disponibles...\n');

async function listDatabases() {
  let connection;
  
  try {
    // Connexion sans spécifier de base de données
    console.log('📡 Connexion à MySQL...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connexion MySQL établie');

    // Lister toutes les bases de données
    console.log('\n📋 Bases de données disponibles:');
    const [databases] = await connection.execute('SHOW DATABASES');
    
    if (databases.length === 0) {
      console.log('⚠️  Aucune base de données trouvée');
    } else {
      databases.forEach((db, index) => {
        const dbName = db.Database;
        console.log(`  ${index + 1}. ${dbName}`);
      });
    }

    // Rechercher spécifiquement les bases liées à GestiCom
    console.log('\n🔍 Recherche de bases de données GestiCom:');
    const gesticomDatabases = databases.filter(db => 
      db.Database.toLowerCase().includes('gesticom')
    );

    if (gesticomDatabases.length === 0) {
      console.log('❌ Aucune base de données GestiCom trouvée');
      console.log('\n💡 Bases de données possibles à créer:');
      console.log('  - gesticom');
      console.log('  - gesticom_sql');
      console.log('  - gesticom_mysql');
    } else {
      console.log('✅ Bases de données GestiCom trouvées:');
      gesticomDatabases.forEach(db => {
        console.log(`  - ${db.Database}`);
      });
    }

    // Vérifier les bases de données système
    console.log('\n📊 Bases de données système détectées:');
    const systemDbs = ['information_schema', 'mysql', 'performance_schema', 'sys'];
    systemDbs.forEach(sysDb => {
      const exists = databases.some(db => db.Database === sysDb);
      console.log(`  ${exists ? '✅' : '❌'} ${sysDb}`);
    });

    console.log('\n🎯 Recommandations:');
    
    if (gesticomDatabases.length > 0) {
      console.log('1. Utilisez une des bases GestiCom existantes');
      console.log('2. Modifiez config/mysql.js avec le bon nom de base');
      console.log('3. Relancez test-connection.js');
    } else {
      console.log('1. Créez une nouvelle base de données GestiCom');
      console.log('2. Lancez setup-database.js pour initialiser');
      console.log('3. Ou utilisez le mode fallback avec DEMARRER.bat');
    }

  } catch (error) {
    console.log('❌ Erreur lors de la connexion:', error.message);
    console.log('\n🔧 Solutions possibles:');
    console.log('1. Vérifiez que MySQL est démarré');
    console.log('2. Vérifiez le nom d\'utilisateur et mot de passe');
    console.log('3. Vérifiez que le port 3306 est correct');
    console.log('4. Installez MySQL si ce n\'est pas fait');
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Exécuter la recherche
listDatabases();
