// Objet pour gérer les fonctionnalités des devis
const quoteManager = {
  getAll: async () => {
    try {
      const response = await fetch('/api/quotes');
      return await response.json();
    } catch (error) {
      console.log("API non disponible, utilisation des données de test");
      // Retourner des données de test si l'API échoue
      return [
        { id: 1, date: '2023-05-15', clientId: 1, clientName: 'Entreprise ABC', total: 1200.50, status: 'En attente' },
        { id: 2, date: '2023-05-20', clientId: 2, clientName: 'Société XYZ', total: 850.75, status: 'Accepté' },
        { id: 3, date: '2023-05-25', clientId: 3, clientName: 'Client Particulier', total: 350.00, status: 'Refusé' }
      ];
    }
  },
  
  // Autres méthodes à implémenter
};

// Fonction d'initialisation pour la section devis
function initQuotesSection() {
  console.log("Initialisation de la section devis");
  
  // Charger la liste des devis
  loadQuotesList();
  
  // Ajouter les gestionnaires d'événements
  document.getElementById('saveQuoteBtn')?.addEventListener('click', saveQuote);
  document.getElementById('searchQuote')?.addEventListener('input', filterQuotes);
  document.getElementById('addQuoteItemBtn')?.addEventListener('click', addQuoteItem);
  
  // Charger les clients pour le sélecteur
  loadClientsForQuote();
  
  // Charger les produits pour le sélecteur
  loadProductsForQuote();
  
  // Initialiser les calculs
  setupQuoteCalculations();
}

// Fonction pour charger la liste des devis
async function loadQuotesList() {
  try {
    const quotes = await quoteManager.getAll();
    const tableBody = document.getElementById('quotesTableBody');
    
    if (quotes.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="6" class="text-center">Aucun devis trouvé</td></tr>';
      return;
    }
    
    tableBody.innerHTML = '';
    quotes.forEach(quote => {
      tableBody.innerHTML += `
        <tr>
          <td>${quote.id}</td>
          <td>${new Date(quote.date).toLocaleDateString()}</td>
          <td>${quote.clientName}</td>
          <td>${quote.total.toFixed(2)} DA</td>
          <td><span class="badge ${getBadgeClass(quote.status)}">${quote.status}</span></td>
          <td>
            <button class="btn btn-sm btn-outline-primary edit-quote" data-id="${quote.id}">
              <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm btn-outline-success print-quote" data-id="${quote.id}">
              <i class="bi bi-printer"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-quote" data-id="${quote.id}">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        </tr>
      `;
    });
    
    // Ajouter les gestionnaires d'événements pour les boutons d'action
    addQuoteActionHandlers();
  } catch (error) {
    console.error('Erreur lors du chargement des devis:', error);
    document.getElementById('quotesTableBody').innerHTML = 
      '<tr><td colspan="6" class="text-center text-danger">Erreur lors du chargement des devis</td></tr>';
  }
}

// Fonction pour obtenir la classe de badge selon le statut
function getBadgeClass(status) {
  switch(status) {
    case 'Accepté':
      return 'bg-success';
    case 'Refusé':
      return 'bg-danger';
    case 'En attente':
    default:
      return 'bg-warning';
  }
}

// Fonction pour filtrer les devis selon la recherche
function filterQuotes() {
  const searchTerm = document.getElementById('searchQuote').value.toLowerCase();
  const rows = document.querySelectorAll('#quotesTableBody tr');
  
  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm) ? '' : 'none';
  });
}

// Fonction pour charger les clients dans le sélecteur
async function loadClientsForQuote() {
  try {
    const clients = await clientManager.getAll();
    const clientSelect = document.getElementById('quoteClient');
    
    // Conserver l'option par défaut
    clientSelect.innerHTML = '<option value="">Sélectionner un client</option>';
    
    clients.forEach(client => {
      const option = document.createElement('option');
      option.value = client.id;
      option.textContent = client.name;
      clientSelect.appendChild(option);
    });
  } catch (error) {
    console.error('Erreur lors du chargement des clients:', error);
  }
}

// Fonction pour charger les produits dans le sélecteur
async function loadProductsForQuote() {
  try {
    const products = await productManager.getAll();
    const productSelects = document.querySelectorAll('.product-select');
    
    productSelects.forEach(select => {
      // Conserver l'option par défaut
      select.innerHTML = '<option value="">Sélectionner un produit</option>';
      
      products.forEach(product => {
        const option = document.createElement('option');
        option.value = product.id;
        option.textContent = product.name;
        option.dataset.price = product.price;
        select.appendChild(option);
      });
      
      // Ajouter le gestionnaire d'événement pour le changement de produit
      select.addEventListener('change', updateProductPrice);
    });
  } catch (error) {
    console.error('Erreur lors du chargement des produits:', error);
  }
}

// Fonction pour mettre à jour le prix du produit sélectionné
function updateProductPrice(e) {
  const select = e.target;
  const row = select.closest('.quote-item');
  const priceInput = row.querySelector('.product-price');
  const quantityInput = row.querySelector('.product-quantity');
  
  if (select.selectedIndex > 0) {
    const option = select.options[select.selectedIndex];
    const price = parseFloat(option.dataset.price);
    priceInput.value = price.toFixed(2);
    
    // Mettre à jour le total de la ligne
    updateLineTotal(row);
  } else {
    priceInput.value = '';
  }
}

// Fonction pour mettre à jour le total d'une ligne
function updateLineTotal(row) {
  const priceInput = row.querySelector('.product-price');
  const quantityInput = row.querySelector('.product-quantity');
  const totalInput = row.querySelector('.product-total');
  
  const price = parseFloat(priceInput.value) || 0;
  const quantity = parseInt(quantityInput.value) || 0;
  const total = price * quantity;
  
  totalInput.value = total.toFixed(2) + ' DA';
  
  // Mettre à jour les totaux du devis
  updateQuoteTotals();
}

// Fonction pour mettre à jour les totaux du devis
function updateQuoteTotals() {
  const rows = document.querySelectorAll('.quote-item');
  let subtotal = 0;
  
  rows.forEach(row => {
    const totalText = row.querySelector('.product-total').value;
    const total = parseFloat(totalText.replace(' DA', '')) || 0;
    subtotal += total;
  });
  
  const tax = subtotal * 0.19; // TVA à 19%
  const total = subtotal + tax;
  
  document.getElementById('quoteSubtotal').value = subtotal.toFixed(2) + ' DA';
  document.getElementById('quoteTax').value = tax.toFixed(2) + ' DA';
  document.getElementById('quoteTotal').value = total.toFixed(2) + ' DA';
}

// Fonction pour configurer les calculs du devis
function setupQuoteCalculations() {
  // Ajouter les gestionnaires d'événements pour les changements de quantité et de prix
  document.addEventListener('input', function(e) {
    if (e.target.classList.contains('product-quantity') || e.target.classList.contains('product-price')) {
      const row = e.target.closest('.quote-item');
      if (row) {
        updateLineTotal(row);
      }
    }
  });
}

// Fonction pour ajouter une ligne d'article au devis
function addQuoteItem() {
  const container = document.getElementById('quoteItems');
  const newRow = document.createElement('div');
  newRow.className = 'row mb-2 quote-item';
  newRow.innerHTML = `
    <div class="col-md-5">
      <select class="form-select product-select" required>
        <option value="">Sélectionner un produit</option>
        <!-- Options chargées dynamiquement -->
      </select>
    </div>
    <div class="col-md-2">
      <input type="number" class="form-control product-quantity" placeholder="Qté" min="1" value="1" required>
    </div>
    <div class="col-md-2">
      <input type="number" step="0.01" class="form-control product-price" placeholder="Prix" required>
    </div>
    <div class="col-md-2">
      <input type="text" class="form-control product-total" placeholder="Total" readonly>
    </div>
    <div class="col-md-1">
      <button type="button" class="btn btn-outline-danger remove-item">
        <i class="bi bi-trash"></i>
      </button>
    </div>
  `;
  container.appendChild(newRow);
  
  // Charger les produits pour le nouveau sélecteur
  const select = newRow.querySelector('.product-select');
  loadProductsForSelect(select);
  
  // Ajouter le gestionnaire d'événement pour le bouton de suppression
  const removeBtn = newRow.querySelector('.remove-item');
  removeBtn.addEventListener('click', function() {
    container.removeChild(newRow);
    updateQuoteTotals();
  });
}

// Fonction pour charger les produits dans un sélecteur spécifique
async function loadProductsForSelect(select) {
  try {
    const products = await productManager.getAll();
    
    // Conserver l'option par défaut
    select.innerHTML = '<option value="">Sélectionner un produit</option>';
    
    products.forEach(product => {
      const option = document.createElement('option');
      option.value = product.id;
      option.textContent = product.name;
      option.dataset.price = product.price;
      select.appendChild(option);
    });
    
    // Ajouter le gestionnaire d'événement pour le changement de produit
    select.addEventListener('change', updateProductPrice);
  } catch (error) {
    console.error('Erreur lors du chargement des produits:', error);
  }
}

// Fonction pour sauvegarder un devis
async function saveQuote() {
  // À implémenter
  console.log("Sauvegarde du devis");
}

// Fonction pour ajouter les gestionnaires d'événements aux boutons d'action
function addQuoteActionHandlers() {
  // À implémenter
  console.log("Ajout des gestionnaires d'événements pour les boutons d'action");
}



