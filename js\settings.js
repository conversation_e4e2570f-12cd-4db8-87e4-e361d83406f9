// Objet pour gérer les paramètres
const settingsManager = {
  // Récupérer tous les paramètres
  getAll: function() {
    const settings = localStorage.getItem('companySettings');
    return settings ? JSON.parse(settings) : this.getDefaultSettings();
  },
  
  // Enregistrer les paramètres
  save: function(settings) {
    localStorage.setItem('companySettings', JSON.stringify(settings));
    return true;
  },
  
  // Paramètres par défaut
  getDefaultSettings: function() {
    return {
      company: {
        name: 'GestiCom',
        address: '123 Rue du Commerce, Alger',
        phone: '021 23 45 67',
        email: '<EMAIL>',
        taxId: '',
        article: '',
        register: '',
        logo: null
      },
      billing: {
        vatRate: 19,
        invoicePrefix: 'FACT-',
        quotePrefix: 'DEV-',
        deliveryPrefix: 'BL-'
      },
      printing: {
        footerText: 'Merci pour votre confiance',
        termsAndConditions: 'Paiement à réception de facture'
      }
    };
  }
};

// Fonction d'initialisation pour la section paramètres
function initSettingsSection() {
  // Charger les paramètres existants
  loadSettings();
  
  // Ajouter les gestionnaires d'événements
  document.getElementById('companySettingsForm').addEventListener('submit', saveCompanySettings);
  document.getElementById('billingSettingsForm').addEventListener('submit', saveBillingSettings);
  document.getElementById('printSettingsForm').addEventListener('submit', savePrintSettings);
  
  // Gestionnaire pour la prévisualisation du logo
  document.getElementById('companyLogo').addEventListener('change', previewLogo);
}

// Fonction pour charger les paramètres
function loadSettings() {
  const settings = settingsManager.getAll();
  
  // Remplir le formulaire des paramètres de l'entreprise
  document.getElementById('companyName').value = settings.company.name;
  document.getElementById('companyAddress').value = settings.company.address;
  document.getElementById('companyPhone').value = settings.company.phone;
  document.getElementById('companyEmail').value = settings.company.email;
  document.getElementById('companyTaxId').value = settings.company.taxId;
  document.getElementById('companyArticle').value = settings.company.article;
  document.getElementById('companyRegister').value = settings.company.register;
  
  // Afficher le logo s'il existe
  if (settings.company.logo) {
    document.getElementById('logoPreview').src = settings.company.logo;
    document.getElementById('logoPreview').classList.remove('d-none');
  }
  
  // Remplir le formulaire des paramètres de facturation
  document.getElementById('vatRate').value = settings.billing.vatRate;
  document.getElementById('invoicePrefix').value = settings.billing.invoicePrefix;
  document.getElementById('quotePrefix').value = settings.billing.quotePrefix;
  document.getElementById('deliveryPrefix').value = settings.billing.deliveryPrefix;
  
  // Remplir le formulaire des paramètres d'impression
  document.getElementById('footerText').value = settings.printing.footerText;
  document.getElementById('termsAndConditions').value = settings.printing.termsAndConditions;
}

// Fonction pour enregistrer les paramètres de l'entreprise
function saveCompanySettings(e) {
  e.preventDefault();
  
  const settings = settingsManager.getAll();
  
  // Mettre à jour les paramètres de l'entreprise
  settings.company.name = document.getElementById('companyName').value;
  settings.company.address = document.getElementById('companyAddress').value;
  settings.company.phone = document.getElementById('companyPhone').value;
  settings.company.email = document.getElementById('companyEmail').value;
  settings.company.taxId = document.getElementById('companyTaxId').value;
  settings.company.article = document.getElementById('companyArticle').value;
  settings.company.register = document.getElementById('companyRegister').value;
  
  // Enregistrer les paramètres
  if (settingsManager.save(settings)) {
    showAlert('companySettingsForm', 'success', 'Paramètres de l\'entreprise enregistrés avec succès');
  } else {
    showAlert('companySettingsForm', 'danger', 'Erreur lors de l\'enregistrement des paramètres');
  }
}

// Fonction pour enregistrer les paramètres de facturation
function saveBillingSettings(e) {
  e.preventDefault();
  
  const settings = settingsManager.getAll();
  
  // Mettre à jour les paramètres de facturation
  settings.billing.vatRate = parseFloat(document.getElementById('vatRate').value);
  settings.billing.invoicePrefix = document.getElementById('invoicePrefix').value;
  settings.billing.quotePrefix = document.getElementById('quotePrefix').value;
  settings.billing.deliveryPrefix = document.getElementById('deliveryPrefix').value;
  
  // Enregistrer les paramètres
  if (settingsManager.save(settings)) {
    showAlert('billingSettingsForm', 'success', 'Paramètres de facturation enregistrés avec succès');
  } else {
    showAlert('billingSettingsForm', 'danger', 'Erreur lors de l\'enregistrement des paramètres');
  }
}

// Fonction pour enregistrer les paramètres d'impression
function savePrintSettings(e) {
  e.preventDefault();
  
  const settings = settingsManager.getAll();
  
  // Mettre à jour les paramètres d'impression
  settings.printing.footerText = document.getElementById('footerText').value;
  settings.printing.termsAndConditions = document.getElementById('termsAndConditions').value;
  
  // Enregistrer les paramètres
  if (settingsManager.save(settings)) {
    showAlert('printSettingsForm', 'success', 'Paramètres d\'impression enregistrés avec succès');
  } else {
    showAlert('printSettingsForm', 'danger', 'Erreur lors de l\'enregistrement des paramètres');
  }
}

// Fonction pour prévisualiser le logo
function previewLogo(e) {
  const file = e.target.files[0];
  if (file) {
    const reader = new FileReader();
    reader.onload = function(event) {
      const settings = settingsManager.getAll();
      settings.company.logo = event.target.result;
      settingsManager.save(settings);
      
      document.getElementById('logoPreview').src = event.target.result;
      document.getElementById('logoPreview').classList.remove('d-none');
    };
    reader.readAsDataURL(file);
  }
}

// Fonction pour afficher une alerte
function showAlert(formId, type, message) {
  const form = document.getElementById(formId);
  
  // Supprimer les alertes existantes
  const existingAlerts = form.querySelectorAll('.alert');
  existingAlerts.forEach(alert => alert.remove());
  
  // Créer une nouvelle alerte
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} mt-3`;
  alertDiv.textContent = message;
  
  // Ajouter l'alerte au formulaire
  form.appendChild(alertDiv);
  
  // Faire disparaître l'alerte après 3 secondes
  setTimeout(() => {
    alertDiv.classList.add('fade');
    setTimeout(() => alertDiv.remove(), 500);
  }, 3000);
}