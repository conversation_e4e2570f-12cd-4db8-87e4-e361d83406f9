// Configuration de la connexion à la base de données PostgreSQL
const dbConfig = {
  host: '*************', // Adresse IP fixe locale
  port: 5432,
  database: 'gesticom',
  user: 'postgres',
  password: 'azed1234',
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};

module.exports = dbConfig;

// Vérification de la connexion à la base de données
const checkDatabaseConnection = async () => {
  try {
    // Tester la connexion à la base de données
    const client = new Client({
      user: 'postgres',
      host: 'localhost',
      database: 'gesticom',
      password: 'votre_mot_de_passe',
      port: 5432,
    });
    
    await client.connect();
    console.log('Connexion à la base de données réussie');
    await client.end();
    return true;
  } catch (error) {
    console.error('Erreur de connexion à la base de données:', error);
    return false;
  }
};




