// Script pour tester la connexion à la base de données gesticom_sql
const db = require('./config/mysql');

console.log('🔍 Test de connexion à la base de données gesticom...\n');

async function testConnection() {
  try {
    // Test de connexion
    console.log('📡 Connexion à MySQL...');
    const isConnected = await db.testConnection();

    if (!isConnected) {
      console.log('❌ Impossible de se connecter à la base de données');
      console.log('💡 Vérifiez que MySQL est démarré et que la base "gesticom" existe');
      return;
    }

    console.log('✅ Connexion réussie à gesticom');

    // Lister les tables
    console.log('\n📋 Tables disponibles dans gesticom:');
    try {
      const tables = await db.query('SHOW TABLES');
      if (tables.length === 0) {
        console.log('⚠️  Aucune table trouvée dans la base de données');
      } else {
        tables.forEach((table, index) => {
          const tableName = Object.values(table)[0];
          console.log(`  ${index + 1}. ${tableName}`);
        });
      }
    } catch (error) {
      console.log('❌ Erreur lors de la récupération des tables:', error.message);
    }

    // Tester les tables principales
    console.log('\n🔍 Vérification des tables principales:');
    const expectedTables = ['products', 'clients', 'suppliers', 'invoices', 'users'];

    for (const tableName of expectedTables) {
      try {
        const result = await db.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        const count = result[0].count;
        console.log(`  ✅ ${tableName}: ${count} enregistrements`);
      } catch (error) {
        console.log(`  ❌ ${tableName}: Table non trouvée ou erreur`);
      }
    }

    // Test de récupération des produits
    console.log('\n📦 Test de récupération des produits:');
    try {
      const products = await db.query('SELECT id, name, price, stockQuantity FROM products LIMIT 5');
      if (products.length === 0) {
        console.log('⚠️  Aucun produit trouvé');
      } else {
        console.log(`✅ ${products.length} produits récupérés:`);
        products.forEach(product => {
          console.log(`  - ${product.name} (${product.price}€, stock: ${product.stockQuantity})`);
        });
      }
    } catch (error) {
      console.log('❌ Erreur lors de la récupération des produits:', error.message);
    }

    // Test de récupération des clients
    console.log('\n👥 Test de récupération des clients:');
    try {
      const clients = await db.query('SELECT id, name, email FROM clients LIMIT 5');
      if (clients.length === 0) {
        console.log('⚠️  Aucun client trouvé');
      } else {
        console.log(`✅ ${clients.length} clients récupérés:`);
        clients.forEach(client => {
          console.log(`  - ${client.name} (${client.email})`);
        });
      }
    } catch (error) {
      console.log('❌ Erreur lors de la récupération des clients:', error.message);
    }

    console.log('\n🎉 Test de connexion terminé avec succès !');
    console.log('💡 Vous pouvez maintenant démarrer l\'application avec DEMARRER-HYBRIDE.bat');

  } catch (error) {
    console.log('❌ Erreur lors du test:', error.message);
    console.log('\n🔧 Solutions possibles:');
    console.log('1. Vérifiez que MySQL est démarré');
    console.log('2. Vérifiez que la base "gesticom_sql" existe');
    console.log('3. Vérifiez les paramètres de connexion dans config/mysql.js');
    console.log('4. Vérifiez le mot de passe MySQL');
  }
}

// Exécuter le test
testConnection();
