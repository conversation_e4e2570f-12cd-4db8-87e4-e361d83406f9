@echo off
title GestiCom - Actions Fonctionnelles
color 0A

echo.
echo ==========================================
echo   GESTICOM - ACTIONS FONCTIONNELLES
echo ==========================================
echo.

echo CORRECTIONS APPLIQUEES:
echo ✅ Fichier utils.js cree avec toutes les fonctions
echo ✅ ProductManager unifie et fonctionnel
echo ✅ Routes CRUD completes (Create, Read, Update, Delete)
echo ✅ Notifications (showSuccess, showError) operationnelles
echo ✅ Gestionnaires d'evenements corriges
echo ✅ Redirection vers index.html corrigee
echo.

echo Test de la connexion MySQL...
node quick-test.js

echo.
echo Demarrage du serveur avec toutes les actions...
echo.

REM Arreter tous les processus Node.js existants
taskkill /F /IM node.exe >nul 2>&1

REM Attendre 2 secondes
ping 127.0.0.1 -n 3 > nul

REM Demarrer le serveur
start "GestiCom Actions Server" /MIN node simple-server.js

REM Attendre 4 secondes
ping 127.0.0.1 -n 5 > nul

echo Ouverture de l'application...
start http://localhost:3000/login.html

echo.
echo ==========================================
echo   GESTICOM - TOUTES LES ACTIONS OK !
echo ==========================================
echo.
echo Application: http://localhost:3000/login.html
echo Test Actions: http://localhost:3000/test-actions.html
echo API Status: http://localhost:3000/api/status
echo.
echo Base de donnees: gesticom (MySQL)
echo.
echo Comptes de connexion:
echo.
echo   admin / admin    (Administrateur)
echo   demo / demo      (Demonstration)
echo.
echo ACTIONS MAINTENANT FONCTIONNELLES:
echo ✅ Ajouter un produit (bouton +)
echo ✅ Modifier un produit (bouton crayon)
echo ✅ Supprimer un produit (bouton poubelle)
echo ✅ Rechercher des produits
echo ✅ Notifications de succes/erreur
echo ✅ Sauvegarde en base MySQL
echo.
echo PAGES DE TEST DISPONIBLES:
echo - http://localhost:3000/test-actions.html
echo - http://localhost:3000/test-products-fix.html
echo.
echo Pour arreter l'application:
echo - Fermez la fenetre "GestiCom Actions Server"
echo - Ou fermez cette fenetre
echo.

pause
