// Configuration de la base de données MySQL
const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '', // Modifiez selon votre configuration MySQL
  database: 'gesticom',
  port: 3306,
  charset: 'utf8mb4',
  connectionLimit: 10
};

// Créer un pool de connexions
const pool = mysql.createPool(dbConfig);

// Fonction pour tester la connexion
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ Connexion à la base de données MySQL réussie');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ Erreur de connexion à la base de données:', error.message);
    return false;
  }
}

// Fonction pour exécuter une requête
async function query(sql, params = []) {
  try {
    const [results] = await pool.execute(sql, params);
    return results;
  } catch (error) {
    console.error('Erreur lors de l\'exécution de la requête:', error);
    throw error;
  }
}

// Fonction pour obtenir une seule ligne
async function queryOne(sql, params = []) {
  try {
    const results = await query(sql, params);
    return results.length > 0 ? results[0] : null;
  } catch (error) {
    throw error;
  }
}

module.exports = {
  pool,
  query,
  queryOne,
  testConnection,
  dbConfig
};
