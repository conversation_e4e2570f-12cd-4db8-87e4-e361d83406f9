# 🏪 GestiCom - Application de Gestion Commerciale

Application web complète de gestion commerciale avec interface moderne et base de données intégrée.

## 🚀 Démarrage Automatique

### Option 1: Script Batch (Windows)
```bash
# Double-cliquez sur le fichier ou exécutez dans le terminal
start.bat
```

### Option 2: Script PowerShell (Recommandé)
```powershell
# Clic droit > "Exécuter avec PowerShell" ou dans le terminal
.\start.ps1
```

### Option 3: NPM Scripts
```bash
# Démarrage simple
npm start

# Démarrage avec ouverture automatique du navigateur
npm run auto
```

### Option 4: Démarrage Manuel
```bash
# Installer les dépendances (première fois seulement)
npm install

# Démarrer le serveur
node simple-server.js

# Ouvrir dans le navigateur
# http://localhost:3000/login.html
```

## 🔐 Connexion

### Comptes de Démonstration
- **admin** / **admin** (Administrateur)
- **demo** / **demo** (Démonstration)
- **user** / **password** (Utilisateur)
- **test** / **test** (Test)

### Première Connexion
1. L'application s'ouvre automatiquement sur `http://localhost:3000/login.html`
2. Utilisez un des comptes ci-dessus
3. Ou cliquez sur "Connexion Démo" pour un accès rapide

## 📊 Fonctionnalités

### Modules Disponibles
- **📈 Tableau de bord** - Statistiques et aperçu
- **📦 Produits** - Gestion de l'inventaire
- **👥 Clients** - Base de données clients
- **🏢 Fournisseurs** - Gestion des fournisseurs
- **💰 Point de Vente** - Caisse et ventes
- **📄 Devis** - Création et suivi
- **🛒 Commandes** - Bons de commande
- **🧾 Factures** - Facturation
- **🚚 Livraisons** - Bons de livraison
- **📋 Inventaire** - Gestion des stocks
- **↩️ Retours** - Retours clients/fournisseurs
- **⚙️ Paramètres** - Configuration
- **🔧 Administration** - Gestion système

### Données Incluses
- **8 produits** de démonstration avec stocks
- **4 clients** d'exemple
- **3 fournisseurs** configurés
- **Statistiques** calculées en temps réel

## 🛠️ Configuration Technique

### Prérequis
- **Node.js** (version 14 ou supérieure)
- **Navigateur web** moderne (Chrome, Firefox, Edge, Safari)

### Ports Utilisés
- **3000** - Serveur principal de l'application
- **API REST** - Endpoints disponibles sur `/api/*`

### Structure des Fichiers
```
gesticom/
├── start.bat              # Script de démarrage Windows
├── start.ps1              # Script PowerShell
├── simple-server.js       # Serveur principal
├── app.html              # Interface principale
├── login.html            # Page de connexion
├── css/
│   └── app.css           # Styles de l'application
├── js/
│   ├── main.js           # Script principal
│   ├── api.js            # Gestionnaire d'API
│   └── simple-auth.js    # Authentification
├── sections/
│   └── dashboard.html    # Tableau de bord
└── package.json          # Configuration npm
```

## 🔧 Dépannage

### Le serveur ne démarre pas
1. Vérifiez que Node.js est installé: `node --version`
2. Installez les dépendances: `npm install`
3. Vérifiez que le port 3000 est libre
4. Redémarrez en tant qu'administrateur si nécessaire

### L'application ne s'ouvre pas
1. Vérifiez que le serveur est démarré
2. Ouvrez manuellement: `http://localhost:3000/login.html`
3. Vérifiez les logs du serveur pour les erreurs

### Problèmes de connexion
1. Utilisez les comptes de démonstration listés ci-dessus
2. Essayez le bouton "Connexion Démo"
3. Vérifiez la console du navigateur (F12) pour les erreurs

### Données non affichées
1. Vérifiez l'API: `http://localhost:3000/api/status`
2. Consultez les logs du serveur
3. Actualisez la page (F5)

## 📞 Support

### Logs et Diagnostic
- **Logs serveur** - Affichés dans le terminal
- **API Status** - `http://localhost:3000/api/status`
- **Console navigateur** - F12 > Console

### Arrêt de l'Application
- **Ctrl+C** dans le terminal
- **Fermer** la fenêtre du terminal
- **Arrêt automatique** en fermant le navigateur (optionnel)

## 🎯 Utilisation

1. **Démarrez** l'application avec un des scripts
2. **Connectez-vous** avec un compte de démonstration
3. **Explorez** les différents modules
4. **Testez** les fonctionnalités de gestion
5. **Consultez** les statistiques en temps réel

L'application est maintenant **entièrement autonome** et se lance automatiquement ! 🎉
