<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Cartes Colorées - Mode Sombre</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  <!-- Theme CSS -->
  <link href="css/themes.css" rel="stylesheet">
</head>
<body>
  <div class="container mt-4">
    <div class="row">
      <div class="col-12">
        <h1>Test des Cartes Colorées en Mode Sombre</h1>
        <p>Cette page teste spécifiquement les cartes colorées du tableau de bord.</p>
        
        <div class="mb-3">
          <button id="toggleTheme" class="btn btn-primary">
            <i class="bi bi-moon-fill me-2"></i>Basculer le thème
          </button>
          <button id="fixCards" class="btn btn-warning ms-2">
            <i class="bi bi-palette me-2"></i>Corriger les cartes
          </button>
          <button id="testDashboard" class="btn btn-info ms-2">
            <i class="bi bi-speedometer2 me-2"></i>Charger tableau de bord
          </button>
        </div>

        <!-- Reproduction des cartes du tableau de bord -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="card text-white bg-primary">
              <div class="card-body">
                <h5 class="card-title">Total Produits</h5>
                <p class="card-text display-4">4</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-white bg-success">
              <div class="card-body">
                <h5 class="card-title">Stock Total</h5>
                <p class="card-text display-4">100</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-white bg-warning">
              <div class="card-body">
                <h5 class="card-title">Stock Faible</h5>
                <p class="card-text display-4">0</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-white bg-danger">
              <div class="card-body">
                <h5 class="card-title">Rupture de Stock</h5>
                <p class="card-text display-4">0</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Cartes supplémentaires pour test -->
        <div class="row mb-4">
          <div class="col-md-4">
            <div class="card text-white bg-info">
              <div class="card-body">
                <h5 class="card-title">Carte Info</h5>
                <p class="card-text">Texte sur fond cyan</p>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="card text-white bg-dark">
              <div class="card-body">
                <h5 class="card-title">Carte Sombre</h5>
                <p class="card-text">Texte sur fond sombre</p>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="card bg-light">
              <div class="card-body">
                <h5 class="card-title">Carte Claire</h5>
                <p class="card-text">Texte sur fond clair</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Informations de diagnostic -->
        <div class="card">
          <div class="card-header">
            <h5>Diagnostic des Couleurs</h5>
          </div>
          <div class="card-body">
            <div id="colorDiagnostic">
              <p><strong>Thème actuel:</strong> <span id="currentTheme">-</span></p>
              <p><strong>Problèmes détectés:</strong> <span id="issuesCount">-</span></p>
              <div id="issuesList"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <!-- Theme JS -->
  <script src="js/theme.js"></script>
  <!-- Theme Diagnostic -->
  <script src="js/theme-diagnostic.js"></script>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const toggleBtn = document.getElementById('toggleTheme');
      const fixBtn = document.getElementById('fixCards');
      const testBtn = document.getElementById('testDashboard');

      // Bouton de basculement de thème
      toggleBtn.addEventListener('click', function() {
        if (window.themeManager) {
          window.themeManager.toggleTheme();
          updateDiagnostic();
          updateToggleButton();
        }
      });

      // Bouton de correction des cartes
      fixBtn.addEventListener('click', function() {
        if (window.forceColoredCardsText) {
          window.forceColoredCardsText();
          updateDiagnostic();
        }
      });

      // Bouton de test du tableau de bord
      testBtn.addEventListener('click', function() {
        window.location.href = 'index.html#dashboard';
      });

      // Fonction de diagnostic des couleurs
      function updateDiagnostic() {
        const currentTheme = window.themeManager ? window.themeManager.getCurrentTheme() : 'Non disponible';
        document.getElementById('currentTheme').textContent = currentTheme;

        let issues = [];
        
        // Vérifier les cartes colorées
        const coloredCards = document.querySelectorAll('.bg-primary, .bg-success, .bg-danger, .bg-warning, .bg-info, .bg-dark');
        coloredCards.forEach((card, index) => {
          const cardClass = Array.from(card.classList).find(cls => cls.startsWith('bg-'));
          const textElements = card.querySelectorAll('h5, p, span, div');
          
          textElements.forEach(element => {
            const color = window.getComputedStyle(element).color;
            const expectedColor = (cardClass === 'bg-warning' || cardClass === 'bg-info') ? 'rgb(0, 0, 0)' : 'rgb(255, 255, 255)';
            
            if (color !== expectedColor) {
              issues.push(`Carte ${cardClass}: couleur incorrecte (${color} au lieu de ${expectedColor})`);
            }
          });
        });

        document.getElementById('issuesCount').textContent = issues.length;
        
        const issuesList = document.getElementById('issuesList');
        if (issues.length === 0) {
          issuesList.innerHTML = '<p class="text-success">✅ Toutes les couleurs sont correctes!</p>';
        } else {
          issuesList.innerHTML = '<ul class="text-danger">' + 
            issues.map(issue => `<li>${issue}</li>`).join('') + 
            '</ul>';
        }
      }

      // Fonction pour mettre à jour le bouton de basculement
      function updateToggleButton() {
        const currentTheme = window.themeManager ? window.themeManager.getCurrentTheme() : 'light';
        if (currentTheme === 'dark') {
          toggleBtn.innerHTML = '<i class="bi bi-sun-fill me-2"></i>Mode clair';
        } else {
          toggleBtn.innerHTML = '<i class="bi bi-moon-fill me-2"></i>Mode sombre';
        }
      }

      // Initialiser
      setTimeout(() => {
        updateDiagnostic();
        updateToggleButton();
      }, 500);

      // Écouter les changements de thème
      document.addEventListener('themeChanged', function(event) {
        console.log('Changement de thème détecté:', event.detail.theme);
        updateDiagnostic();
        updateToggleButton();
      });
    });
  </script>
</body>
</html>
