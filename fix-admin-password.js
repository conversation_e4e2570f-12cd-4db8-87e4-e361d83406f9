// Script simple pour corriger le mot de passe admin
const db = require('./config/mysql');

async function fixAdminPassword() {
  try {
    console.log('🔧 Correction du mot de passe admin...');

    // Mettre à jour le mot de passe admin en texte clair
    await db.query('UPDATE users SET password = ? WHERE username = ?', ['admin', 'admin']);
    
    console.log('✅ Mot de passe admin mis à jour');
    console.log('📋 Vous pouvez maintenant vous connecter avec:');
    console.log('   - Nom d\'utilisateur: admin');
    console.log('   - Mot de passe: admin');

    // Vérifier que la mise à jour a fonctionné
    const user = await db.queryOne('SELECT username, password, role FROM users WHERE username = ?', ['admin']);
    console.log('🔍 Utilisateur admin:', user);

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    process.exit(0);
  }
}

fixAdminPassword();
