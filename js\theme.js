// ===== GESTIONNAIRE DE THÈMES =====

class ThemeManager {
  constructor() {
    this.currentTheme = 'light';
    this.init();
  }

  init() {
    // Charger le thème sauvegardé ou détecter la préférence système
    this.loadSavedTheme();

    // Créer le bouton de basculement
    this.createThemeToggle();

    // Écouter les changements de préférence système
    this.listenToSystemPreference();

    console.log('🎨 Gestionnaire de thèmes initialisé');
  }

  loadSavedTheme() {
    // Vérifier le thème sauvegardé dans localStorage
    const savedTheme = localStorage.getItem('theme');

    if (savedTheme) {
      this.currentTheme = savedTheme;
    } else {
      // Détecter la préférence système
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        this.currentTheme = 'dark';
      }
    }

    this.applyTheme(this.currentTheme);

    // Forcer l'application immédiate si c'est le mode sombre
    if (this.currentTheme === 'dark') {
      this.forceImmediateDarkMode();
    }

    console.log(`🎨 Thème chargé: ${this.currentTheme}`);
  }

  applyTheme(theme) {
    const html = document.documentElement;

    if (theme === 'dark') {
      html.setAttribute('data-theme', 'dark');
      this.currentTheme = 'dark';

      // Forcer immédiatement l'application du mode sombre
      this.forceImmediateDarkMode();
    } else {
      html.removeAttribute('data-theme');
      this.currentTheme = 'light';

      // Supprimer les styles d'urgence si on passe en mode clair
      const emergencyStyles = document.getElementById('emergency-dark-styles');
      if (emergencyStyles) {
        emergencyStyles.remove();
      }
    }

    // Sauvegarder le choix
    localStorage.setItem('theme', this.currentTheme);

    // Forcer la mise à jour des styles sur tous les éléments
    this.forceStyleUpdate();

    // Mettre à jour l'icône du bouton flottant
    this.updateToggleIcon();

    // Mettre à jour le bouton de la sidebar avec retry
    this.updateSidebarButton();

    // Forcer l'application du thème sur tous les éléments
    this.forceThemeApplication();

    console.log(`🎨 Thème appliqué: ${this.currentTheme}`);
  }

  forceStyleUpdate() {
    // Forcer le recalcul des styles en modifiant temporairement une propriété
    const body = document.body;
    const originalDisplay = body.style.display;
    body.style.display = 'none';

    // Forcer le reflow
    body.offsetHeight;

    // Restaurer
    body.style.display = originalDisplay;

    // Déclencher un événement personnalisé pour notifier le changement de thème
    const event = new CustomEvent('themeChanged', {
      detail: { theme: this.currentTheme }
    });
    document.dispatchEvent(event);
  }

  toggleTheme() {
    const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(newTheme);

    // Animation du bouton
    const button = document.getElementById('themeToggle');
    if (button) {
      button.style.transform = 'scale(0.8)';
      setTimeout(() => {
        button.style.transform = 'scale(1)';
      }, 150);
    }
  }

  createThemeToggle() {
    // Vérifier si le bouton existe déjà
    if (document.getElementById('themeToggle')) {
      return;
    }

    const button = document.createElement('button');
    button.id = 'themeToggle';
    button.className = 'btn btn-outline-secondary theme-toggle';
    button.title = 'Basculer le thème';
    button.setAttribute('aria-label', 'Basculer entre le mode clair et sombre');

    // Ajouter l'événement de clic
    button.addEventListener('click', () => this.toggleTheme());

    // Ajouter le bouton au body
    document.body.appendChild(button);

    // Mettre à jour l'icône
    this.updateToggleIcon();

    console.log('🎨 Bouton de thème créé');
  }

  updateToggleIcon() {
    const button = document.getElementById('themeToggle');
    if (!button) return;

    if (this.currentTheme === 'dark') {
      button.innerHTML = '<i class="bi bi-sun-fill"></i>';
      button.title = 'Passer au mode clair';
    } else {
      button.innerHTML = '<i class="bi bi-moon-fill"></i>';
      button.title = 'Passer au mode sombre';
    }
  }

  // Nouvelle méthode pour mettre à jour le bouton de la sidebar avec retry
  updateSidebarButton() {
    const updateButton = () => {
      const button = document.getElementById('sidebarThemeToggle');
      if (!button) return false;

      if (this.currentTheme === 'dark') {
        button.innerHTML = '<i class="bi bi-sun-fill me-2"></i>Mode clair';
        button.title = 'Passer au mode clair';
      } else {
        button.innerHTML = '<i class="bi bi-moon-fill me-2"></i>Mode sombre';
        button.title = 'Passer au mode sombre';
      }
      return true;
    };

    // Essayer immédiatement
    if (!updateButton()) {
      // Si le bouton n'existe pas encore, réessayer après un délai
      setTimeout(() => {
        if (!updateButton()) {
          // Dernier essai après un délai plus long
          setTimeout(updateButton, 500);
        }
      }, 100);
    }

    // Aussi appeler la fonction globale si elle existe
    if (window.updateSidebarThemeButton) {
      setTimeout(window.updateSidebarThemeButton, 50);
    }
  }

  // Nouvelle méthode pour forcer l'application du thème
  forceThemeApplication() {
    if (this.currentTheme === 'dark') {
      // Forcer l'application des styles sombres
      setTimeout(() => {
        this.forceDarkModeStyles();
      }, 100);
    }
  }

  // Méthode pour forcer l'application immédiate du mode sombre
  forceImmediateDarkMode() {
    console.log('🚨 Forçage immédiat du mode sombre');

    const html = document.documentElement;
    html.setAttribute('data-theme', 'dark');

    // Ajouter des styles CSS directement dans le head
    this.injectEmergencyDarkStyles();

    // Forcer les styles sur tous les éléments existants
    this.forceDarkModeStyles();

    // Répéter plusieurs fois pour s'assurer que ça marche
    setTimeout(() => this.forceDarkModeStyles(), 100);
    setTimeout(() => this.forceDarkModeStyles(), 500);
    setTimeout(() => this.forceDarkModeStyles(), 1000);
  }

  // Méthode pour injecter des styles CSS d'urgence
  injectEmergencyDarkStyles() {
    // Vérifier si les styles d'urgence existent déjà
    if (document.getElementById('emergency-dark-styles')) {
      return;
    }

    const style = document.createElement('style');
    style.id = 'emergency-dark-styles';
    style.textContent = `
      [data-theme="dark"] #stock-value,
      [data-theme="dark"] #low-stock-list,
      [data-theme="dark"] #low-stock-list *,
      [data-theme="dark"] .list-group-item,
      [data-theme="dark"] #dashboard-container h2,
      [data-theme="dark"] #dashboard-container .card-body *:not(.btn):not(.badge) {
        color: #ffffff !important;
      }

      [data-theme="dark"] .list-group-item {
        background-color: #495057 !important;
        border-color: #6c757d !important;
      }

      [data-theme="dark"] #dashboard-container .card.bg-warning *,
      [data-theme="dark"] #dashboard-container .card.bg-info * {
        color: #000000 !important;
      }
    `;
    document.head.appendChild(style);
    console.log('🚨 Styles CSS d\'urgence injectés');
  }

  // Méthode pour forcer les styles du mode sombre
  forceDarkModeStyles() {
    const html = document.documentElement;

    // S'assurer que l'attribut data-theme est bien présent
    if (this.currentTheme === 'dark' && !html.hasAttribute('data-theme')) {
      html.setAttribute('data-theme', 'dark');
    }

    // Forcer spécifiquement les éléments du tableau de bord
    const stockValue = document.getElementById('stock-value');
    if (stockValue) {
      stockValue.style.setProperty('color', '#ffffff', 'important');
    }

    const lowStockList = document.getElementById('low-stock-list');
    if (lowStockList) {
      lowStockList.style.setProperty('color', '#ffffff', 'important');
      const listItems = lowStockList.querySelectorAll('li');
      listItems.forEach(item => {
        item.style.setProperty('background-color', '#495057', 'important');
        item.style.setProperty('color', '#ffffff', 'important');
        item.style.setProperty('border-color', '#6c757d', 'important');
      });
    }

    // Forcer la couleur du texte sur les éléments problématiques
    const problematicElements = document.querySelectorAll('body, p, div, span, h1, h2, h3, h4, h5, h6, td, th, li, label');
    problematicElements.forEach(element => {
      if (this.currentTheme === 'dark') {
        // Éviter les cartes colorées
        const isInColoredCard = element.closest('.bg-primary, .bg-success, .bg-danger, .bg-dark');
        const isInWarningCard = element.closest('.bg-warning, .bg-info');

        if (isInWarningCard) {
          // Laisser le texte noir sur les cartes warning/info
          return;
        }

        const computedStyle = window.getComputedStyle(element);
        if (computedStyle.color === 'rgb(0, 0, 0)' ||
            computedStyle.color === '#000000' ||
            computedStyle.color === 'black' ||
            computedStyle.color === 'rgb(33, 37, 41)' ||
            computedStyle.color === 'rgb(52, 58, 64)') {
          element.style.setProperty('color', '#ffffff', 'important');
        }
      }
    });
  }

  listenToSystemPreference() {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

      mediaQuery.addEventListener('change', (e) => {
        // Ne changer automatiquement que si l'utilisateur n'a pas de préférence sauvegardée
        if (!localStorage.getItem('theme')) {
          const newTheme = e.matches ? 'dark' : 'light';
          this.applyTheme(newTheme);
          console.log(`🎨 Thème automatique changé: ${newTheme}`);
        }
      });
    }
  }

  // Méthode pour obtenir le thème actuel
  getCurrentTheme() {
    return this.currentTheme;
  }

  // Méthode pour forcer un thème spécifique
  setTheme(theme) {
    if (theme === 'light' || theme === 'dark') {
      this.applyTheme(theme);
    }
  }

  // Méthode pour réinitialiser aux préférences système
  resetToSystemPreference() {
    localStorage.removeItem('theme');

    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      this.applyTheme('dark');
    } else {
      this.applyTheme('light');
    }

    console.log('🎨 Thème réinitialisé aux préférences système');
  }
}

// ===== FONCTIONS UTILITAIRES =====

// Fonction pour ajouter des classes de transition aux éléments
function addThemeTransitions() {
  const elements = document.querySelectorAll('body, .card, .table, .form-control, .btn, .modal-content, .sidebar');
  elements.forEach(el => {
    el.classList.add('theme-transition');
  });
}

// Fonction pour appliquer le thème aux nouveaux éléments dynamiques
function applyThemeToNewElements(container) {
  if (!container) return;

  const elements = container.querySelectorAll('.card, .table, .form-control, .btn, .modal-content');
  elements.forEach(el => {
    el.classList.add('theme-transition');
  });
}

// ===== INITIALISATION =====

// Créer l'instance globale du gestionnaire de thèmes
let themeManager;

// Initialiser quand le DOM est prêt
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    themeManager = new ThemeManager();
    addThemeTransitions();
    setupMutationObserver();
  });
} else {
  themeManager = new ThemeManager();
  addThemeTransitions();
  setupMutationObserver();
}

// Écouter les changements de thème pour forcer la mise à jour
document.addEventListener('themeChanged', function(event) {
  console.log('🎨 Événement de changement de thème détecté:', event.detail.theme);

  // Forcer la mise à jour de tous les éléments visibles
  setTimeout(() => {
    const allElements = document.querySelectorAll('*');
    allElements.forEach(element => {
      // Forcer le recalcul des styles en touchant une propriété
      const computedStyle = window.getComputedStyle(element);
      element.style.color = computedStyle.color;
    });
  }, 50);
});

// Fonction améliorée pour forcer la couleur blanche sur tous les éléments en mode sombre
function forceWhiteTextInDarkMode() {
  if (document.documentElement.getAttribute('data-theme') === 'dark') {
    const allTextElements = document.querySelectorAll('*:not(.btn):not(.badge):not(.alert):not(.btn-warning):not(.btn-info):not(.btn-light)');
    allTextElements.forEach(element => {
      // Ignorer les éléments dans des cartes colorées
      const isInColoredCard = element.closest('.bg-primary, .bg-success, .bg-danger, .bg-warning, .bg-info, .bg-dark');
      if (isInColoredCard) {
        return; // Laisser le CSS gérer ces éléments
      }

      const computedStyle = window.getComputedStyle(element);
      const currentColor = computedStyle.color;

      // Vérifier si l'élément a une couleur noire ou très sombre
      if (currentColor === 'rgb(0, 0, 0)' ||
          currentColor === '#000000' ||
          currentColor === 'black' ||
          currentColor === 'rgb(33, 37, 41)' || // Bootstrap text-dark
          currentColor === '#212529') {
        element.style.setProperty('color', '#ffffff', 'important');
      }
    });

    // Forcer spécifiquement certains éléments (sauf dans les cartes colorées)
    const specificElements = document.querySelectorAll('.card-title, .card-text, .table td, .table th, .form-label, .nav-link');
    specificElements.forEach(element => {
      const isInColoredCard = element.closest('.bg-primary, .bg-success, .bg-danger, .bg-warning, .bg-info, .bg-dark');
      if (isInColoredCard) {
        return; // Laisser le CSS gérer ces éléments
      }

      if (window.getComputedStyle(element).color !== 'rgb(255, 255, 255)') {
        element.style.setProperty('color', '#ffffff', 'important');
      }
    });

    // Forcer spécifiquement les cartes colorées
    forceColoredCardsText();
  }
}

// Fonction spécifique pour les cartes colorées
function forceColoredCardsText() {
  // Cartes avec fond bleu, vert, rouge, sombre - texte blanc
  const whiteTextCards = document.querySelectorAll('.bg-primary, .bg-success, .bg-danger, .bg-dark');
  whiteTextCards.forEach(card => {
    const allElements = card.querySelectorAll('*');
    allElements.forEach(element => {
      element.style.setProperty('color', '#ffffff', 'important');
    });
  });

  // Cartes avec fond jaune, cyan - texte noir
  const blackTextCards = document.querySelectorAll('.bg-warning, .bg-info');
  blackTextCards.forEach(card => {
    const allElements = card.querySelectorAll('*');
    allElements.forEach(element => {
      element.style.setProperty('color', '#000000', 'important');
    });
  });
}

// Observateur de mutations pour appliquer le thème aux nouveaux éléments
function setupMutationObserver() {
  if (!window.themeObserver) {
    window.themeObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Appliquer le thème aux nouveaux éléments
              if (window.applyThemeToNewElements) {
                window.applyThemeToNewElements(node);
              }

              // Forcer les styles sombres si nécessaire avec plusieurs tentatives
              if (document.documentElement.getAttribute('data-theme') === 'dark') {
                // Tentative immédiate
                setTimeout(() => {
                  forceWhiteTextInDarkMode();
                  if (window.forceThemeOnContainer) {
                    window.forceThemeOnContainer(node);
                  }
                }, 10);

                // Tentative retardée
                setTimeout(() => {
                  forceWhiteTextInDarkMode();
                  if (window.forceThemeOnContainer) {
                    window.forceThemeOnContainer(node);
                  }
                }, 100);

                // Tentative finale
                setTimeout(() => {
                  forceWhiteTextInDarkMode();
                  if (window.forceThemeOnContainer) {
                    window.forceThemeOnContainer(node);
                  }
                }, 300);
              }
            }
          });
        }
      });
    });

    // Observer les changements dans le body
    window.themeObserver.observe(document.body, {
      childList: true,
      subtree: true
    });

    console.log('🎨 Observateur de mutations configuré pour le thème');
  }
}

// Fonction d'urgence pour forcer le mode sombre
window.emergencyDarkMode = function() {
  console.log('🚨 Mode sombre d\'urgence activé');

  // Forcer l'attribut data-theme
  document.documentElement.setAttribute('data-theme', 'dark');

  // Forcer les styles sur tous les éléments
  const allElements = document.querySelectorAll('*');
  allElements.forEach(element => {
    const tagName = element.tagName.toLowerCase();

    // Éléments de texte
    if (['p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'td', 'th', 'li', 'label', 'a'].includes(tagName)) {
      if (!element.classList.contains('btn') && !element.classList.contains('badge') && !element.classList.contains('alert')) {
        element.style.setProperty('color', '#ffffff', 'important');
      }
    }

    // Éléments de fond
    if (['body', 'main', 'nav', 'header', 'footer', 'section', 'article'].includes(tagName)) {
      element.style.setProperty('background-color', '#1a1a1a', 'important');
    }

    // Cartes
    if (element.classList.contains('card')) {
      element.style.setProperty('background-color', '#2d2d2d', 'important');
      element.style.setProperty('border-color', '#404040', 'important');
    }

    // Sidebar
    if (element.classList.contains('sidebar')) {
      element.style.setProperty('background-color', '#2d2d2d', 'important');
    }

    // Tableaux
    if (element.classList.contains('table')) {
      element.style.setProperty('background-color', '#2d2d2d', 'important');
      element.style.setProperty('color', '#ffffff', 'important');
    }

    // Formulaires
    if (element.classList.contains('form-control') || element.classList.contains('form-select')) {
      element.style.setProperty('background-color', '#3d3d3d', 'important');
      element.style.setProperty('border-color', '#555555', 'important');
      element.style.setProperty('color', '#ffffff', 'important');
    }
  });

  console.log('🚨 Mode sombre d\'urgence appliqué');
};

// Fonction pour réinitialiser complètement le thème
window.resetTheme = function() {
  console.log('🔄 Réinitialisation complète du thème');

  // Supprimer tous les styles inline
  const allElements = document.querySelectorAll('*[style]');
  allElements.forEach(element => {
    element.removeAttribute('style');
  });

  // Réinitialiser l'attribut data-theme
  document.documentElement.removeAttribute('data-theme');

  // Supprimer le thème du localStorage
  localStorage.removeItem('theme');

  // Recharger le gestionnaire de thème
  if (window.themeManager) {
    window.themeManager.loadSavedTheme();
  }

  console.log('🔄 Thème réinitialisé');
};

// Exporter pour utilisation globale
window.themeManager = themeManager;
window.applyThemeToNewElements = applyThemeToNewElements;
window.forceWhiteTextInDarkMode = forceWhiteTextInDarkMode;
window.forceColoredCardsText = forceColoredCardsText;
window.setupMutationObserver = setupMutationObserver;

// Fonction de correction rapide pour le mode sombre
window.quickDarkModeFix = function() {
  console.log('🔧 Correction rapide du mode sombre...');

  if (document.documentElement.getAttribute('data-theme') === 'dark') {
    // Utiliser la méthode du themeManager si disponible
    if (window.themeManager && window.themeManager.forceDarkModeStyles) {
      window.themeManager.forceDarkModeStyles();
    }

    // Forcer immédiatement
    forceWhiteTextInDarkMode();

    // Forcer sur le container principal
    const container = document.getElementById('app-container');
    if (container && window.forceThemeOnContainer) {
      window.forceThemeOnContainer(container);
    }

    // Correction spécifique pour les éléments du tableau de bord
    const stockValue = document.getElementById('stock-value');
    if (stockValue) {
      stockValue.style.setProperty('color', '#ffffff', 'important');
    }

    const lowStockList = document.getElementById('low-stock-list');
    if (lowStockList) {
      lowStockList.style.setProperty('color', '#ffffff', 'important');
      const listItems = lowStockList.querySelectorAll('li');
      listItems.forEach(item => {
        item.style.setProperty('background-color', '#495057', 'important');
        item.style.setProperty('color', '#ffffff', 'important');
        item.style.setProperty('border-color', '#6c757d', 'important');
      });
    }

    // Correction spécifique pour les cartes du tableau de bord
    const dashboardCards = document.querySelectorAll('.card.bg-primary, .card.bg-success, .card.bg-warning, .card.bg-danger, .card.bg-info');
    dashboardCards.forEach(card => {
      const cardElements = card.querySelectorAll('*');
      cardElements.forEach(element => {
        if (card.classList.contains('bg-warning') || card.classList.contains('bg-info')) {
          element.style.setProperty('color', '#000000', 'important');
        } else {
          element.style.setProperty('color', '#ffffff', 'important');
        }
      });
    });

    // Forcer le texte blanc sur tous les éléments principaux
    const mainElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div, td, th, li, label');
    mainElements.forEach(element => {
      // Éviter les boutons, badges et alertes
      if (!element.closest('.btn') && !element.closest('.badge') && !element.closest('.alert')) {
        // Vérifier si l'élément est dans une carte colorée
        const coloredCard = element.closest('.bg-primary, .bg-success, .bg-danger, .bg-dark');
        const warningCard = element.closest('.bg-warning, .bg-info');

        if (warningCard) {
          element.style.setProperty('color', '#000000', 'important');
        } else if (coloredCard || !element.closest('.bg-warning, .bg-info')) {
          element.style.setProperty('color', '#ffffff', 'important');
        }
      }
    });

    // Correction spéciale pour les éléments avec du texte noir
    const allVisibleElements = document.querySelectorAll('*:not([style*="display: none"]):not([style*="visibility: hidden"])');
    allVisibleElements.forEach(element => {
      const computedStyle = window.getComputedStyle(element);
      const currentColor = computedStyle.color;

      // Si l'élément a du texte noir, le forcer en blanc (sauf exceptions)
      if ((currentColor === 'rgb(0, 0, 0)' ||
          currentColor === '#000000' ||
          currentColor === 'rgb(33, 37, 41)' ||
          currentColor === 'rgb(52, 58, 64)') &&
          !element.closest('.btn-warning, .btn-info, .btn-light, .bg-warning, .bg-info')) {
        element.style.setProperty('color', '#ffffff', 'important');
      }
    });

    console.log('✅ Correction rapide appliquée avec améliorations pour les cartes colorées');
  }
};

// Fonction globale pour forcer le mode sombre sur le tableau de bord
window.forceDashboardDarkMode = function() {
  console.log('🚨 Forçage du mode sombre sur le tableau de bord');

  // Forcer l'attribut data-theme
  document.documentElement.setAttribute('data-theme', 'dark');

  // Appliquer les corrections
  if (window.themeManager && window.themeManager.forceDarkModeStyles) {
    window.themeManager.forceDarkModeStyles();
  }

  if (window.quickDarkModeFix) {
    window.quickDarkModeFix();
  }

  if (window.emergencyDashboardThemeFix) {
    window.emergencyDashboardThemeFix();
  }
};

// Ajouter un raccourci clavier pour la correction rapide
document.addEventListener('keydown', function(event) {
  // Ctrl + Shift + F pour correction rapide
  if (event.ctrlKey && event.shiftKey && event.key === 'F') {
    event.preventDefault();
    window.quickDarkModeFix();
    console.log('🔧 Correction rapide activée via raccourci clavier (Ctrl+Shift+F)');
  }
});

console.log('🎨 Module de thèmes chargé');
console.log('💡 Fonctions d\'urgence disponibles: emergencyDarkMode(), resetTheme(), quickDarkModeFix()');
console.log('💡 Diagnostic disponible: runThemeDiagnostic(), fixAllThemeIssues()');
console.log('⌨️ Raccourci clavier: Ctrl+Shift+F pour correction rapide');
