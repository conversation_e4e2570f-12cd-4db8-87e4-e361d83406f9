<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Actions - GestiCom</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test des Actions - GestiCom</h1>
        
        <div class="alert alert-info">
            <strong>Test complet des actions CRUD (Create, Read, Update, Delete)</strong>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Tests API</h3>
                <div class="d-grid gap-2">
                    <button id="testGetProducts" class="btn btn-primary">Test GET /api/products</button>
                    <button id="testGetProduct" class="btn btn-primary">Test GET /api/products/1</button>
                    <button id="testCreateProduct" class="btn btn-success">Test POST /api/products</button>
                    <button id="testUpdateProduct" class="btn btn-warning">Test PUT /api/products/1</button>
                    <button id="testDeleteProduct" class="btn btn-danger">Test DELETE /api/products/999</button>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>Tests Interface</h3>
                <div class="d-grid gap-2">
                    <button id="testProductManager" class="btn btn-info">Test ProductManager</button>
                    <button id="testNotifications" class="btn btn-info">Test Notifications</button>
                    <button id="testLoadProducts" class="btn btn-info">Test loadProductsList()</button>
                    <button id="testSaveProduct" class="btn btn-info">Test saveProduct()</button>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Résultats des Tests</h3>
            <div id="testResults" class="border p-3" style="height: 400px; overflow-y: auto; background-color: #f8f9fa;">
                <div class="text-muted">Les résultats des tests apparaîtront ici...</div>
            </div>
        </div>
        
        <div class="mt-3">
            <button id="runAllTests" class="btn btn-success btn-lg">Exécuter Tous les Tests</button>
            <button id="clearResults" class="btn btn-outline-secondary">Effacer les Résultats</button>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Utilitaires -->
    <script src="js/utils.js"></script>

    <script>
        const resultsDiv = document.getElementById('testResults');
        
        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const alertClass = type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info';
            resultsDiv.innerHTML += `
                <div class="alert alert-${alertClass} py-2 mb-2">
                    <small>[${timestamp}]</small> ${message}
                </div>
            `;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Test GET /api/products
        document.getElementById('testGetProducts').addEventListener('click', async () => {
            addResult('🧪 Test GET /api/products...', 'info');
            try {
                const response = await fetch('/api/products');
                const data = await response.json();
                addResult(`✅ Succès: ${data.length} produits récupérés`, 'success');
                addResult(`📊 Premier produit: ${data[0]?.name || 'Aucun'}`, 'info');
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Test GET /api/products/:id
        document.getElementById('testGetProduct').addEventListener('click', async () => {
            addResult('🧪 Test GET /api/products/1...', 'info');
            try {
                const response = await fetch('/api/products/1');
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Succès: Produit "${data.name}" récupéré`, 'success');
                } else {
                    addResult(`⚠️ Produit non trouvé (${response.status})`, 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Test POST /api/products
        document.getElementById('testCreateProduct').addEventListener('click', async () => {
            addResult('🧪 Test POST /api/products...', 'info');
            try {
                const testProduct = {
                    name: 'Produit Test',
                    description: 'Produit de test',
                    price: 99.99,
                    stockQuantity: 10,
                    category_id: 1
                };
                
                const response = await fetch('/api/products', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testProduct)
                });
                
                const data = await response.json();
                if (response.ok) {
                    addResult(`✅ Succès: Produit créé avec ID ${data.id}`, 'success');
                } else {
                    addResult(`❌ Erreur création: ${data.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Test PUT /api/products/:id
        document.getElementById('testUpdateProduct').addEventListener('click', async () => {
            addResult('🧪 Test PUT /api/products/1...', 'info');
            try {
                const updateData = {
                    name: 'Produit Modifié',
                    price: 199.99,
                    stockQuantity: 5
                };
                
                const response = await fetch('/api/products/1', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(updateData)
                });
                
                const data = await response.json();
                if (response.ok) {
                    addResult(`✅ Succès: Produit mis à jour`, 'success');
                } else {
                    addResult(`❌ Erreur mise à jour: ${data.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Test DELETE /api/products/:id
        document.getElementById('testDeleteProduct').addEventListener('click', async () => {
            addResult('🧪 Test DELETE /api/products/999...', 'info');
            try {
                const response = await fetch('/api/products/999', {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                if (response.ok) {
                    addResult(`✅ Succès: Produit supprimé`, 'success');
                } else {
                    addResult(`⚠️ Produit non trouvé (normal pour test)`, 'info');
                }
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Test ProductManager
        document.getElementById('testProductManager').addEventListener('click', async () => {
            addResult('🧪 Test ProductManager...', 'info');
            try {
                if (typeof productManager !== 'undefined') {
                    addResult('✅ ProductManager disponible', 'success');
                    const products = await productManager.getAll();
                    addResult(`✅ ProductManager.getAll(): ${products.length} produits`, 'success');
                } else {
                    addResult('❌ ProductManager non disponible', 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur ProductManager: ${error.message}`, 'error');
            }
        });
        
        // Test Notifications
        document.getElementById('testNotifications').addEventListener('click', () => {
            addResult('🧪 Test des notifications...', 'info');
            try {
                if (typeof showSuccess !== 'undefined') {
                    showSuccess('Test notification de succès');
                    addResult('✅ showSuccess() fonctionne', 'success');
                } else {
                    addResult('❌ showSuccess() non disponible', 'error');
                }
                
                if (typeof showError !== 'undefined') {
                    showError('Test notification d\'erreur');
                    addResult('✅ showError() fonctionne', 'success');
                } else {
                    addResult('❌ showError() non disponible', 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur notifications: ${error.message}`, 'error');
            }
        });
        
        // Test loadProductsList
        document.getElementById('testLoadProducts').addEventListener('click', () => {
            addResult('🧪 Test loadProductsList()...', 'info');
            try {
                if (typeof loadProductsList !== 'undefined') {
                    addResult('✅ loadProductsList() disponible', 'success');
                    // Note: On ne l'exécute pas car elle nécessite le DOM de la page produits
                } else {
                    addResult('❌ loadProductsList() non disponible', 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Test saveProduct
        document.getElementById('testSaveProduct').addEventListener('click', () => {
            addResult('🧪 Test saveProduct()...', 'info');
            try {
                if (typeof saveProduct !== 'undefined') {
                    addResult('✅ saveProduct() disponible', 'success');
                    // Note: On ne l'exécute pas car elle nécessite le DOM de la page produits
                } else {
                    addResult('❌ saveProduct() non disponible', 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Exécuter tous les tests
        document.getElementById('runAllTests').addEventListener('click', async () => {
            addResult('🚀 Exécution de tous les tests...', 'info');
            
            // Délai entre chaque test
            const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
            
            document.getElementById('testGetProducts').click();
            await delay(500);
            document.getElementById('testGetProduct').click();
            await delay(500);
            document.getElementById('testProductManager').click();
            await delay(500);
            document.getElementById('testNotifications').click();
            await delay(500);
            document.getElementById('testCreateProduct').click();
            await delay(500);
            document.getElementById('testUpdateProduct').click();
            await delay(500);
            document.getElementById('testDeleteProduct').click();
            
            addResult('🎉 Tous les tests terminés !', 'success');
        });
        
        // Effacer les résultats
        document.getElementById('clearResults').addEventListener('click', () => {
            resultsDiv.innerHTML = '<div class="text-muted">Résultats effacés...</div>';
        });
        
        // Test automatique au chargement
        window.addEventListener('load', () => {
            addResult('🚀 Page de test chargée', 'info');
            addResult('💡 Cliquez sur "Exécuter Tous les Tests" pour commencer', 'info');
        });
    </script>
</body>
</html>
