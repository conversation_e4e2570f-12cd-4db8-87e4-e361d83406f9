const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const port = 3000;

console.log('🚀 Démarrage du serveur GestiCom...');

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('./'));

// Middleware de logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Variable pour tracker l'état de MySQL
let mysqlAvailable = false;
let db = null;

// Tenter de charger la configuration MySQL
try {
  db = require('./config/mysql');

  // Tester la connexion MySQL
  db.testConnection().then(result => {
    mysqlAvailable = result;
    if (mysqlAvailable) {
      console.log('✅ MySQL connecté - utilisation de la base de données');
    } else {
      console.log('⚠️ MySQL non disponible - utilisation des données de fallback');
    }
  }).catch(error => {
    console.log('⚠️ MySQL non disponible - utilisation des données de fallback');
    mysqlAvailable = false;
  });

} catch (error) {
  console.log('⚠️ Configuration MySQL non trouvée - utilisation des données de fallback');
  mysqlAvailable = false;
}

// Données de fallback enrichies
const fallbackData = {
  products: [
    { id: 1, name: "Ordinateur portable Dell", description: "PC portable haute performance 16GB RAM", price: 89999.99, stockQuantity: 15, category: "Informatique", barcode: "123456789", sku: "ORD-001", minStockLevel: 5 },
    { id: 2, name: "iPhone 15 Pro", description: "Smartphone Apple dernière génération", price: 149999.99, stockQuantity: 8, category: "Téléphonie", barcode: "987654321", sku: "TEL-001", minStockLevel: 10 },
    { id: 3, name: "Écran Samsung 27 pouces", description: "Moniteur LED 4K 27 pouces", price: 35999.99, stockQuantity: 12, category: "Informatique", barcode: "456789123", sku: "ECR-001", minStockLevel: 3 },
    { id: 4, name: "Souris Logitech MX Master", description: "Souris sans fil ergonomique", price: 8999.99, stockQuantity: 25, category: "Accessoires", barcode: "789123456", sku: "SOU-001", minStockLevel: 15 },
    { id: 5, name: "Clavier mécanique", description: "Clavier gaming RGB", price: 12999.99, stockQuantity: 18, category: "Accessoires", barcode: "321654987", sku: "CLA-001", minStockLevel: 8 },
    { id: 6, name: "Imprimante HP LaserJet", description: "Imprimante laser noir et blanc", price: 25999.99, stockQuantity: 6, category: "Bureautique", barcode: "654321789", sku: "IMP-001", minStockLevel: 5 },
    { id: 7, name: "Webcam Logitech 4K", description: "Caméra web haute définition", price: 15999.99, stockQuantity: 9, category: "Accessoires", barcode: "147258369", sku: "WEB-001", minStockLevel: 5 },
    { id: 8, name: "Disque dur externe 2TB", description: "Stockage externe portable", price: 8999.99, stockQuantity: 22, category: "Stockage", barcode: "963852741", sku: "HDD-001", minStockLevel: 10 }
  ],
  clients: [
    { id: 1, name: "Dupont Entreprise", contactPerson: "Jean Dupont", email: "<EMAIL>", phone: "01 23 45 67 89", address: "123 Rue de Paris", city: "Paris", postalCode: "75001", country: "France" },
    { id: 2, name: "Martin SARL", contactPerson: "Marie Martin", email: "<EMAIL>", phone: "01 98 76 54 32", address: "45 Avenue des Champs", city: "Paris", postalCode: "75008", country: "France" },
    { id: 3, name: "Petit Commerce", contactPerson: "Pierre Petit", email: "<EMAIL>", phone: "01 45 67 89 10", address: "78 Boulevard Haussmann", city: "Paris", postalCode: "75009", country: "France" },
    { id: 4, name: "TechCorp Solutions", contactPerson: "Sophie Dubois", email: "<EMAIL>", phone: "01 55 66 77 88", address: "15 Rue de la Tech", city: "Lyon", postalCode: "69001", country: "France" }
  ],
  suppliers: [
    { id: 1, name: "Tech Supplies Inc.", contactPerson: "John Smith", email: "<EMAIL>", phone: "01 23 45 67 89", address: "123 Tech Street", city: "Paris", postalCode: "75001", country: "France" },
    { id: 2, name: "Global Electronics", contactPerson: "Anna Johnson", email: "<EMAIL>", phone: "01 98 76 54 32", address: "45 Electronics Avenue", city: "Paris", postalCode: "75008", country: "France" },
    { id: 3, name: "Office Solutions", contactPerson: "Mike Wilson", email: "<EMAIL>", phone: "01 45 67 89 10", address: "78 Office Boulevard", city: "Paris", postalCode: "75009", country: "France" }
  ],
  invoices: [
    { id: 1, invoiceNumber: "FACT-001", date: "2024-01-15", clientName: "Dupont Entreprise", total: 150000, status: "paid", clientId: 1 },
    { id: 2, invoiceNumber: "FACT-002", date: "2024-01-16", clientName: "Martin SARL", total: 75000, status: "unpaid", clientId: 2 },
    { id: 3, invoiceNumber: "FACT-003", date: "2024-01-17", clientName: "TechCorp Solutions", total: 220000, status: "paid", clientId: 4 }
  ]
};

// Route de statut
app.get('/api/status', (req, res) => {
  console.log('📊 Vérification du statut du serveur');
  res.json({
    status: 'OK',
    mysql: mysqlAvailable,
    timestamp: new Date().toISOString(),
    message: mysqlAvailable ? 'Base de données MySQL connectée' : 'Utilisation des données de fallback',
    dataSource: mysqlAvailable ? 'MySQL Database' : 'Fallback Data',
    productsCount: fallbackData.products.length,
    clientsCount: fallbackData.clients.length
  });
});

// Routes API
app.get('/api/products', async (req, res) => {
  console.log('📦 GET /api/products - Récupération des produits');

  try {
    if (mysqlAvailable && db) {
      // Adapter les noms de colonnes à votre structure
      const products = await db.query(`
        SELECT
          id,
          name,
          description,
          price,
          stock_quantity as stockQuantity,
          category_id,
          barcode,
          sku,
          min_stock_level as minStockLevel,
          created_at,
          updated_at
        FROM products
        ORDER BY name
      `);
      console.log(`✅ ${products.length} produits récupérés depuis MySQL`);
      res.json(products);
    } else {
      console.log('⚠️ Utilisation des données de fallback');
      res.json(fallbackData.products);
    }
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des produits:', error);
    res.json(fallbackData.products);
  }
});

app.get('/api/clients', async (req, res) => {
  console.log('👥 GET /api/clients - Récupération des clients');

  try {
    if (mysqlAvailable && db) {
      // Adapter les noms de colonnes à votre structure
      const clients = await db.query(`
        SELECT
          id,
          name,
          contact_person as contactPerson,
          email,
          phone,
          address,
          city,
          postal_code as postalCode,
          country,
          tax_id,
          created_at,
          updated_at
        FROM clients
        ORDER BY name
      `);
      console.log(`✅ ${clients.length} clients récupérés depuis MySQL`);
      res.json(clients);
    } else {
      console.log('⚠️ Utilisation des données de fallback');
      res.json(fallbackData.clients);
    }
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error);
    res.json(fallbackData.clients);
  }
});

app.get('/api/suppliers', async (req, res) => {
  console.log('🏢 GET /api/suppliers - Récupération des fournisseurs');

  try {
    if (mysqlAvailable && db) {
      // Adapter les noms de colonnes à votre structure
      const suppliers = await db.query(`
        SELECT
          id,
          name,
          contact_person as contactPerson,
          email,
          phone,
          address,
          city,
          postal_code as postalCode,
          country,
          tax_id,
          created_at,
          updated_at
        FROM suppliers
        ORDER BY name
      `);
      console.log(`✅ ${suppliers.length} fournisseurs récupérés depuis MySQL`);
      res.json(suppliers);
    } else {
      console.log('⚠️ Utilisation des données de fallback');
      res.json(fallbackData.suppliers);
    }
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des fournisseurs:', error);
    res.json(fallbackData.suppliers);
  }
});

app.get('/api/invoices', async (req, res) => {
  console.log('🧾 GET /api/invoices - Récupération des factures');

  try {
    if (mysqlAvailable && db) {
      // Adapter les noms de colonnes à votre structure
      const invoices = await db.query(`
        SELECT
          i.id,
          i.invoice_number as invoiceNumber,
          i.date,
          i.client_id as clientId,
          c.name as clientName,
          i.total,
          i.status,
          i.due_date,
          i.created_at,
          i.updated_at
        FROM invoices i
        LEFT JOIN clients c ON i.client_id = c.id
        ORDER BY i.date DESC
      `);
      console.log(`✅ ${invoices.length} factures récupérées depuis MySQL`);
      res.json(invoices);
    } else {
      console.log('⚠️ Utilisation des données de fallback');
      res.json(fallbackData.invoices);
    }
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des factures:', error);
    res.json(fallbackData.invoices);
  }
});

// Routes pour les éléments individuels
app.get('/api/products/:id', async (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`📦 GET /api/products/${id} - Récupération du produit`);

  try {
    if (mysqlAvailable && db) {
      const products = await db.query(`
        SELECT
          id,
          name,
          description,
          price,
          stock_quantity as stockQuantity,
          category_id,
          barcode,
          sku,
          min_stock_level as minStockLevel,
          created_at,
          updated_at
        FROM products
        WHERE id = ?
      `, [id]);

      if (products.length > 0) {
        console.log(`✅ Produit ${id} trouvé: ${products[0].name}`);
        res.json(products[0]);
      } else {
        console.log(`❌ Produit ${id} non trouvé`);
        res.status(404).json({ error: 'Produit non trouvé' });
      }
    } else {
      const product = fallbackData.products.find(p => p.id === id);
      if (product) {
        console.log(`📦 GET /api/products/${id} - Produit trouvé: ${product.name}`);
        res.json(product);
      } else {
        console.log(`📦 GET /api/products/${id} - Produit non trouvé`);
        res.status(404).json({ error: 'Produit non trouvé' });
      }
    }
  } catch (error) {
    console.error('❌ Erreur lors de la récupération du produit:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// Route pour créer un produit
app.post('/api/products', async (req, res) => {
  console.log('📦 POST /api/products - Création d\'un produit:', req.body);

  try {
    if (mysqlAvailable && db) {
      const { name, description, price, stockQuantity, category_id, barcode, sku, minStockLevel } = req.body;

      const result = await db.query(`
        INSERT INTO products (name, description, price, stock_quantity, category_id, barcode, sku, min_stock_level)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [name, description || '', price || 0, stockQuantity || 0, category_id || null, barcode || null, sku || null, minStockLevel || 0]);

      console.log(`✅ Produit créé avec l'ID: ${result.insertId}`);
      res.json({ id: result.insertId, message: 'Produit créé avec succès' });
    } else {
      // Mode fallback - simulation
      const newId = Math.max(...fallbackData.products.map(p => p.id)) + 1;
      const newProduct = { id: newId, ...req.body };
      fallbackData.products.push(newProduct);
      console.log(`✅ Produit créé (fallback) avec l'ID: ${newId}`);
      res.json({ id: newId, message: 'Produit créé avec succès (mode test)' });
    }
  } catch (error) {
    console.error('❌ Erreur lors de la création du produit:', error);
    res.status(500).json({ error: 'Erreur lors de la création du produit' });
  }
});

// Route pour mettre à jour un produit
app.put('/api/products/:id', async (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`📦 PUT /api/products/${id} - Mise à jour du produit:`, req.body);

  try {
    if (mysqlAvailable && db) {
      const { name, description, price, stockQuantity, category_id, barcode, sku, minStockLevel } = req.body;

      const result = await db.query(`
        UPDATE products
        SET name = ?, description = ?, price = ?, stock_quantity = ?, category_id = ?, barcode = ?, sku = ?, min_stock_level = ?
        WHERE id = ?
      `, [name, description || '', price || 0, stockQuantity || 0, category_id || null, barcode || null, sku || null, minStockLevel || 0, id]);

      if (result.affectedRows > 0) {
        console.log(`✅ Produit ${id} mis à jour avec succès`);
        res.json({ message: 'Produit mis à jour avec succès' });
      } else {
        console.log(`❌ Produit ${id} non trouvé pour mise à jour`);
        res.status(404).json({ error: 'Produit non trouvé' });
      }
    } else {
      // Mode fallback - simulation
      const productIndex = fallbackData.products.findIndex(p => p.id === id);
      if (productIndex !== -1) {
        fallbackData.products[productIndex] = { ...fallbackData.products[productIndex], ...req.body };
        console.log(`✅ Produit ${id} mis à jour (fallback)`);
        res.json({ message: 'Produit mis à jour avec succès (mode test)' });
      } else {
        res.status(404).json({ error: 'Produit non trouvé' });
      }
    }
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du produit:', error);
    res.status(500).json({ error: 'Erreur lors de la mise à jour du produit' });
  }
});

// Route pour supprimer un produit
app.delete('/api/products/:id', async (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`📦 DELETE /api/products/${id} - Suppression du produit`);

  try {
    if (mysqlAvailable && db) {
      const result = await db.query('DELETE FROM products WHERE id = ?', [id]);

      if (result.affectedRows > 0) {
        console.log(`✅ Produit ${id} supprimé avec succès`);
        res.json({ message: 'Produit supprimé avec succès' });
      } else {
        console.log(`❌ Produit ${id} non trouvé pour suppression`);
        res.status(404).json({ error: 'Produit non trouvé' });
      }
    } else {
      // Mode fallback - simulation
      const productIndex = fallbackData.products.findIndex(p => p.id === id);
      if (productIndex !== -1) {
        fallbackData.products.splice(productIndex, 1);
        console.log(`✅ Produit ${id} supprimé (fallback)`);
        res.json({ message: 'Produit supprimé avec succès (mode test)' });
      } else {
        res.status(404).json({ error: 'Produit non trouvé' });
      }
    }
  } catch (error) {
    console.error('❌ Erreur lors de la suppression du produit:', error);
    res.status(500).json({ error: 'Erreur lors de la suppression du produit' });
  }
});

app.get('/api/clients/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const client = fallbackData.clients.find(c => c.id === id);
  if (client) {
    console.log(`👥 GET /api/clients/${id} - Client trouvé: ${client.name}`);
    res.json(client);
  } else {
    console.log(`👥 GET /api/clients/${id} - Client non trouvé`);
    res.status(404).json({ error: 'Client non trouvé' });
  }
});

// Route pour servir l'application
app.get('/', (req, res) => {
  console.log('🏠 GET / - Redirection vers app.html');
  res.sendFile(path.join(__dirname, 'app.html'));
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({ error: 'Erreur interne du serveur' });
});

// Démarrage du serveur
app.listen(port, () => {
  console.log('');
  console.log('🎉 ===== SERVEUR GESTICOM DÉMARRÉ =====');
  console.log(`📍 URL principale: http://localhost:${port}`);
  console.log(`🌐 Application: http://localhost:${port}/app.html`);
  console.log(`🔐 Connexion: http://localhost:${port}/login.html`);
  console.log(`📊 Statut API: http://localhost:${port}/api/status`);
  console.log(`📦 Produits: ${fallbackData.products.length} disponibles`);
  console.log(`👥 Clients: ${fallbackData.clients.length} enregistrés`);
  console.log(`🏢 Fournisseurs: ${fallbackData.suppliers.length} disponibles`);
  console.log('');
  console.log('💡 Pour arrêter le serveur, appuyez sur Ctrl+C');
  console.log('==========================================');
});

// Gestion de l'arrêt propre
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur GestiCom...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt du serveur GestiCom...');
  process.exit(0);
});
