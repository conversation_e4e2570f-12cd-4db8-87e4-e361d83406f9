<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fix Produits</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test de Correction - Page Produits</h1>
        
        <div class="alert alert-info">
            <strong>Test de la section produits avec les corrections appliquées</strong>
        </div>
        
        <!-- Inclure la section produits directement -->
        <div id="products-section">
            <div class="row mb-3">
              <div class="col">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                  <i class="bi bi-plus-circle me-2"></i>Nouveau produit
                </button>
                <button type="button" class="btn btn-outline-primary ms-2" data-bs-toggle="modal" data-bs-target="#manageCategoriesModal">
                  <i class="bi bi-tags me-2"></i>Gérer les catégories
                </button>
              </div>
              <div class="col-md-4">
                <div class="input-group">
                  <input type="text" class="form-control" id="searchProduct" placeholder="Rechercher un produit...">
                  <button class="btn btn-outline-secondary" type="button">
                    <i class="bi bi-search"></i>
                  </button>
                </div>
              </div>
            </div>

            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Nom</th>
                    <th>Prix</th>
                    <th>Stock</th>
                    <th>Catégorie</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody id="productsTableBody">
                  <tr>
                    <td colspan="6" class="text-center">Chargement en cours...</td>
                  </tr>
                </tbody>
              </table>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Logs de Test</h3>
            <div id="testLogs" class="border p-3" style="height: 300px; overflow-y: auto; background-color: #f8f9fa;">
                <div class="text-muted">Les logs apparaîtront ici...</div>
            </div>
        </div>
        
        <div class="mt-3">
            <button id="testBtn" class="btn btn-success">Tester le Chargement des Produits</button>
            <button id="clearLogsBtn" class="btn btn-outline-secondary">Effacer les Logs</button>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Utilitaires -->
    <script src="js/utils.js"></script>
    
    <!-- Products JS -->
    <script src="js/products.js"></script>

    <script>
        // Rediriger les logs vers notre interface
        const originalLog = console.log;
        const originalError = console.error;
        const logsDiv = document.getElementById('testLogs');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logClass = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-dark';
            logsDiv.innerHTML += `<div class="${logClass}">[${timestamp}] ${message}</div>`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog('ERROR: ' + args.join(' '), 'error');
        };
        
        // Test automatique
        document.getElementById('testBtn').addEventListener('click', function() {
            addLog('🧪 Début du test de chargement des produits', 'info');
            
            // Simuler l'initialisation de la section
            if (typeof initProductsSection === 'function') {
                addLog('✅ Fonction initProductsSection trouvée', 'success');
                try {
                    initProductsSection();
                    addLog('✅ initProductsSection() exécutée', 'success');
                } catch (error) {
                    addLog('❌ Erreur dans initProductsSection(): ' + error.message, 'error');
                }
            } else {
                addLog('❌ Fonction initProductsSection non trouvée', 'error');
            }
        });
        
        document.getElementById('clearLogsBtn').addEventListener('click', function() {
            logsDiv.innerHTML = '<div class="text-muted">Logs effacés...</div>';
        });
        
        // Test automatique au chargement
        window.addEventListener('load', function() {
            addLog('🚀 Page chargée, test automatique...', 'info');
            setTimeout(() => {
                document.getElementById('testBtn').click();
            }, 1000);
        });
    </script>
</body>
</html>
