<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GestiCom - Connexion</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  <!-- Custom CSS -->
  <link href="css/app.css" rel="stylesheet">
  <style>
    html, body {
      height: 100%;
    }
    body {
      display: flex;
      align-items: center;
      padding-top: 40px;
      padding-bottom: 40px;
      background-color: var(--bg-secondary);
      transition: background-color 0.3s ease;
    }
    .form-signin {
      width: 100%;
      max-width: 330px;
      padding: 15px;
      margin: auto;
      background-color: var(--card-bg);
      border-radius: 10px;
      box-shadow: 0 4px 6px var(--shadow);
      border: 1px solid var(--border-color);
      transition: all 0.3s ease;
    }
    .form-signin .form-floating:focus-within {
      z-index: 2;
    }
    .form-signin input[type="text"] {
      margin-bottom: -1px;
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;
    }
    .form-signin input[type="password"] {
      margin-bottom: 10px;
      border-top-left-radius: 0;
      border-top-right-radius: 0;
    }
  </style>
</head>
<body class="text-center">
  <main class="form-signin">
    <form id="loginForm">
      <h1 class="h3 mb-3 fw-normal">GestiCom</h1>
      <h2 class="h5 mb-3 fw-normal">Connexion</h2>

      <div class="form-floating">
        <input type="text" class="form-control" id="username" name="username" placeholder="Nom d'utilisateur" required>
        <label for="username">Nom d'utilisateur</label>
      </div>
      <div class="form-floating">
        <input type="password" class="form-control" id="password" name="password" placeholder="Mot de passe" required>
        <label for="password">Mot de passe</label>
      </div>

      <div class="alert alert-danger d-none" id="loginError">
        Nom d'utilisateur ou mot de passe incorrect
      </div>

      <button class="w-100 btn btn-lg btn-primary" type="submit">Se connecter</button>

      <div class="mt-3 p-2 bg-info bg-opacity-10 rounded">
        <small class="text-muted">
          <strong>Comptes de démonstration :</strong><br>
          • admin / admin (Administrateur)<br>
          • demo / demo (Démonstration)<br>
          • user / password (Utilisateur)<br>
          • test / test (Test)
        </small>
      </div>

      <!-- Informations de débogage -->
      <div class="mt-3 p-2 bg-light rounded">
        <small class="text-muted">
          <strong>Informations de connexion:</strong><br>
          URL: <span id="debugUrl"></span><br>
          API: <span id="debugApi"></span><br>
          Statut: <span id="debugStatus" class="badge bg-secondary">Test...</span>
        </small>
      </div>

      <p class="mt-3 mb-3 text-muted">&copy; 2023 GestiCom</p>
    </form>
  </main>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Configuration et Auth Scripts -->
  <script src="js/config.js"></script>
  <script src="js/theme.js"></script>
  <script src="js/simple-auth.js"></script>

  <script>
    // Afficher les informations de débogage
    document.getElementById('debugUrl').textContent = window.location.href;

    // Attendre que la configuration soit chargée
    setTimeout(() => {
      if (window.APP_CONFIG) {
        document.getElementById('debugApi').textContent = window.APP_CONFIG.API_URL;

        // Tester la connexion
        window.testConnection().then(success => {
          const statusElement = document.getElementById('debugStatus');
          if (success) {
            statusElement.textContent = 'Connecté';
            statusElement.className = 'badge bg-success';
          } else {
            statusElement.textContent = 'Déconnecté';
            statusElement.className = 'badge bg-danger';
          }
        }).catch(error => {
          const statusElement = document.getElementById('debugStatus');
          statusElement.textContent = 'Erreur';
          statusElement.className = 'badge bg-danger';
          console.error('Erreur de test de connexion:', error);
        });
      } else {
        document.getElementById('debugApi').textContent = 'Configuration non chargée';
        document.getElementById('debugStatus').textContent = 'Erreur config';
        document.getElementById('debugStatus').className = 'badge bg-danger';
      }
    }, 1000);

    // Le formulaire de connexion est maintenant géré par simple-auth.js
    // Pas besoin de code supplémentaire ici

    // Rediriger si déjà connecté
    if (isAuthenticated && isAuthenticated()) {
      window.location.href = 'app.html';
    }

    // Ajouter l'animation de rotation
    const style = document.createElement('style');
    style.textContent = `
      .spin {
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
  </script>
</body>
</html>
