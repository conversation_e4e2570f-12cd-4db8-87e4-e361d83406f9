// Fichier utilitaires pour GestiCom
console.log('🔧 Chargement des utilitaires...');

// Gestionnaire de notifications
function showNotification(message, type = 'info', duration = 3000) {
  console.log(`📢 Notification ${type}: ${message}`);
  
  // Créer l'élément de notification
  const notification = document.createElement('div');
  notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
  notification.style.cssText = `
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 500px;
  `;
  
  notification.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;
  
  // Ajouter au body
  document.body.appendChild(notification);
  
  // Supprimer automatiquement après la durée spécifiée
  setTimeout(() => {
    if (notification.parentNode) {
      notification.remove();
    }
  }, duration);
}

// Fonctions de notification spécifiques
function showSuccess(message, duration = 3000) {
  showNotification(message, 'success', duration);
}

function showError(message, duration = 5000) {
  showNotification(message, 'error', duration);
}

function showInfo(message, duration = 3000) {
  showNotification(message, 'info', duration);
}

function showWarning(message, duration = 4000) {
  showNotification(message, 'warning', duration);
}

// Gestionnaire de produits
class ProductManager {
  constructor() {
    this.baseUrl = '/api/products';
    console.log('📦 ProductManager initialisé');
  }

  async getAll() {
    try {
      console.log('📦 ProductManager.getAll() - Récupération de tous les produits');
      const response = await fetch(this.baseUrl);
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      const products = await response.json();
      console.log(`✅ ${products.length} produits récupérés via ProductManager`);
      return products;
    } catch (error) {
      console.error('❌ Erreur ProductManager.getAll():', error);
      throw error;
    }
  }

  async getById(id) {
    try {
      console.log(`📦 ProductManager.getById(${id}) - Récupération du produit`);
      const response = await fetch(`${this.baseUrl}/${id}`);
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      const product = await response.json();
      console.log(`✅ Produit ${id} récupéré:`, product);
      return product;
    } catch (error) {
      console.error(`❌ Erreur ProductManager.getById(${id}):`, error);
      throw error;
    }
  }

  async create(productData) {
    try {
      console.log('📦 ProductManager.create() - Création d\'un produit:', productData);
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData)
      });
      
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      
      const result = await response.json();
      console.log('✅ Produit créé avec succès:', result);
      return result;
    } catch (error) {
      console.error('❌ Erreur ProductManager.create():', error);
      throw error;
    }
  }

  async update(id, productData) {
    try {
      console.log(`📦 ProductManager.update(${id}) - Mise à jour du produit:`, productData);
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData)
      });
      
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      
      const result = await response.json();
      console.log(`✅ Produit ${id} mis à jour avec succès:`, result);
      return result;
    } catch (error) {
      console.error(`❌ Erreur ProductManager.update(${id}):`, error);
      throw error;
    }
  }

  async delete(id) {
    try {
      console.log(`📦 ProductManager.delete(${id}) - Suppression du produit`);
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      
      console.log(`✅ Produit ${id} supprimé avec succès`);
      return true;
    } catch (error) {
      console.error(`❌ Erreur ProductManager.delete(${id}):`, error);
      throw error;
    }
  }
}

// Gestionnaire de clients
class ClientManager {
  constructor() {
    this.baseUrl = '/api/clients';
    console.log('👥 ClientManager initialisé');
  }

  async getAll() {
    try {
      const response = await fetch(this.baseUrl);
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('❌ Erreur ClientManager.getAll():', error);
      throw error;
    }
  }

  async getById(id) {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`);
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`❌ Erreur ClientManager.getById(${id}):`, error);
      throw error;
    }
  }
}

// Gestionnaire de fournisseurs
class SupplierManager {
  constructor() {
    this.baseUrl = '/api/suppliers';
    console.log('🏢 SupplierManager initialisé');
  }

  async getAll() {
    try {
      const response = await fetch(this.baseUrl);
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('❌ Erreur SupplierManager.getAll():', error);
      throw error;
    }
  }
}

// Gestionnaire de factures
class InvoiceManager {
  constructor() {
    this.baseUrl = '/api/invoices';
    console.log('🧾 InvoiceManager initialisé');
  }

  async getAll() {
    try {
      const response = await fetch(this.baseUrl);
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('❌ Erreur InvoiceManager.getAll():', error);
      throw error;
    }
  }
}

// Créer les instances globales
const productManager = new ProductManager();
const clientManager = new ClientManager();
const supplierManager = new SupplierManager();
const invoiceManager = new InvoiceManager();

// Fonctions utilitaires diverses
function formatCurrency(amount, currency = 'DA') {
  return `${parseFloat(amount).toFixed(2)} ${currency}`;
}

function formatDate(date) {
  if (!date) return '-';
  return new Date(date).toLocaleDateString('fr-FR');
}

function formatDateTime(date) {
  if (!date) return '-';
  return new Date(date).toLocaleString('fr-FR');
}

// Fonction pour valider les formulaires
function validateForm(formData, rules) {
  const errors = [];
  
  for (const [field, rule] of Object.entries(rules)) {
    const value = formData[field];
    
    if (rule.required && (!value || value.toString().trim() === '')) {
      errors.push(`Le champ "${rule.label || field}" est requis`);
    }
    
    if (value && rule.type === 'number' && isNaN(parseFloat(value))) {
      errors.push(`Le champ "${rule.label || field}" doit être un nombre`);
    }
    
    if (value && rule.type === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      errors.push(`Le champ "${rule.label || field}" doit être un email valide`);
    }
    
    if (value && rule.min && parseFloat(value) < rule.min) {
      errors.push(`Le champ "${rule.label || field}" doit être supérieur à ${rule.min}`);
    }
  }
  
  return errors;
}

// Fonction pour déboguer
function debugLog(message, data = null) {
  console.log(`🐛 DEBUG: ${message}`, data || '');
}

console.log('✅ Utilitaires chargés avec succès');

// Rendre les fonctions globales
window.showNotification = showNotification;
window.showSuccess = showSuccess;
window.showError = showError;
window.showInfo = showInfo;
window.showWarning = showWarning;
window.productManager = productManager;
window.clientManager = clientManager;
window.supplierManager = supplierManager;
window.invoiceManager = invoiceManager;
window.formatCurrency = formatCurrency;
window.formatDate = formatDate;
window.formatDateTime = formatDateTime;
window.validateForm = validateForm;
window.debugLog = debugLog;
