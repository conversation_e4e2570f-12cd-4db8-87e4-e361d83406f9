// Objet pour gérer l'authentification
const auth = {
  // Vérifier si l'utilisateur est connecté
  isLoggedIn: function() {
    return localStorage.getItem('authToken') !== null;
  },

  // Connecter l'utilisateur avec l'API
  login: async function(username, password) {
    console.log('🔐 Tentative de connexion pour:', username);

    // Vérifier d'abord l'authentification locale pour les comptes par défaut
    if (this.checkLocalAuth(username, password)) {
      console.log('✅ Authentification locale réussie');
      const token = 'local-admin-token-' + Math.random().toString(36).substring(2);
      localStorage.setItem('authToken', token);
      localStorage.setItem('username', username);
      localStorage.setItem('userRole', this.getLocalRole(username));
      localStorage.setItem('userId', '1');
      return { success: true, user: { username, role: this.getLocalRole(username) } };
    }

    // Essayer l'authentification via l'API
    try {
      const response = await window.APP_CONFIG.fetch('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ username, password })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Connexion API réussie:', data);

        // Stocker les informations de session
        localStorage.setItem('authToken', data.token || 'authenticated');
        localStorage.setItem('username', data.username || username);
        localStorage.setItem('userRole', data.role || 'user');
        localStorage.setItem('userId', data.id || '1');

        return { success: true, user: data };
      } else {
        const errorData = await response.json();
        console.error('❌ Erreur de connexion API:', errorData);
        return { success: false, error: errorData.error || 'Identifiants incorrects' };
      }
    } catch (error) {
      console.error('❌ Erreur de connexion API:', error);
      return { success: false, error: 'Impossible de se connecter au serveur. Vérifiez vos identifiants.' };
    }
  },

  // Vérifier l'authentification locale
  checkLocalAuth: function(username, password) {
    const localAccounts = {
      'admin': 'admin',
      'manager': 'manager123',
      'cashier': 'cashier123',
      'demo': 'demo'
    };

    return localAccounts[username] === password;
  },

  // Obtenir le rôle local
  getLocalRole: function(username) {
    const localRoles = {
      'admin': 'admin',
      'manager': 'manager',
      'cashier': 'cashier',
      'demo': 'viewer'
    };

    return localRoles[username] || 'viewer';
  },

  // Déconnecter l'utilisateur
  logout: function() {
    console.log('🚪 Déconnexion...');
    localStorage.removeItem('authToken');
    localStorage.removeItem('username');
    localStorage.removeItem('userRole');
    localStorage.removeItem('userId');
    window.location.href = 'login.html';
  },

  // Obtenir le nom d'utilisateur
  getUsername: function() {
    return localStorage.getItem('username') || 'Utilisateur';
  },

  // Obtenir le rôle de l'utilisateur
  getUserRole: function() {
    return localStorage.getItem('userRole') || 'user';
  },

  // Obtenir l'ID de l'utilisateur
  getUserId: function() {
    return localStorage.getItem('userId') || '1';
  },

  // Vérifier si l'utilisateur a un rôle spécifique
  hasRole: function(role) {
    const userRole = this.getUserRole();
    const roleHierarchy = {
      'viewer': 1,
      'cashier': 2,
      'manager': 3,
      'admin': 4,
      'superadmin': 5
    };

    return roleHierarchy[userRole] >= roleHierarchy[role];
  }
};



