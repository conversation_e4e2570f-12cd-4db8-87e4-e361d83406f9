// Objet pour gérer les fonctionnalités de la caisse
const pos = {
  currentSale: {
    items: [],
    client: null,
    paymentMethod: 'cash',
    subtotal: 0,
    tax: 0,
    total: 0
  },
  
  // Réinitialiser la vente courante
  resetSale: function() {
    this.currentSale = {
      items: [],
      client: null,
      paymentMethod: 'cash',
      subtotal: 0,
      tax: 0,
      total: 0
    };
    this.updateCart();
  },
  
  // Ajouter un produit au panier
  addProduct: function(product) {
    // Vérifier si le produit est déjà dans le panier
    const existingItem = this.currentSale.items.find(item => item.id === product.id);
    
    if (existingItem) {
      // Incrémenter la quantité
      existingItem.quantity++;
      existingItem.total = existingItem.quantity * existingItem.price;
    } else {
      // Ajouter un nouvel article
      this.currentSale.items.push({
        id: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        total: product.price
      });
    }
    
    // Mettre à jour l'affichage du panier
    this.updateCart();
  },
  
  // Supprimer un produit du panier
  removeProduct: function(productId) {
    this.currentSale.items = this.currentSale.items.filter(item => item.id !== productId);
    this.updateCart();
  },
  
  // Mettre à jour la quantité d'un produit
  updateQuantity: function(productId, quantity) {
    const item = this.currentSale.items.find(item => item.id === productId);
    if (item) {
      item.quantity = quantity;
      item.total = item.quantity * item.price;
      this.updateCart();
    }
  },
  
  // Mettre à jour l'affichage du panier
  updateCart: function() {
    const cartContainer = document.getElementById('posCartItems');
    const subtotalElement = document.getElementById('posSubtotal');
    const taxElement = document.getElementById('posTax');
    const totalElement = document.getElementById('posTotal');
    const checkoutBtn = document.getElementById('posCheckoutBtn');
    const clearBtn = document.getElementById('posClearBtn');
    
    if (!cartContainer || !subtotalElement || !taxElement || !totalElement) {
      console.error("Éléments du panier non trouvés");
      return;
    }
    
    // Calculer les totaux
    this.currentSale.subtotal = this.currentSale.items.reduce((sum, item) => sum + item.total, 0);
    this.currentSale.tax = this.currentSale.subtotal * 0.19; // TVA à 19%
    this.currentSale.total = this.currentSale.subtotal + this.currentSale.tax;
    
    // Mettre à jour l'affichage des totaux
    subtotalElement.textContent = this.currentSale.subtotal.toFixed(2) + ' DA';
    taxElement.textContent = this.currentSale.tax.toFixed(2) + ' DA';
    totalElement.textContent = this.currentSale.total.toFixed(2) + ' DA';
    
    // Activer/désactiver les boutons selon l'état du panier
    if (checkoutBtn) checkoutBtn.disabled = this.currentSale.items.length === 0;
    if (clearBtn) clearBtn.disabled = this.currentSale.items.length === 0;
    
    // Afficher les articles du panier
    if (this.currentSale.items.length === 0) {
      cartContainer.innerHTML = '<div class="text-center text-muted">Aucun article dans le panier</div>';
      return;
    }
    
    cartContainer.innerHTML = '';
    this.currentSale.items.forEach(item => {
      const itemElement = document.createElement('div');
      itemElement.className = 'mb-2 p-2 border-bottom';
      itemElement.innerHTML = `
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h6 class="mb-0">${item.name}</h6>
            <small class="text-muted">${item.price.toFixed(2)} DA × ${item.quantity}</small>
          </div>
          <div class="d-flex align-items-center">
            <span class="me-2">${item.total.toFixed(2)} DA</span>
            <button class="btn btn-sm btn-outline-danger remove-item" data-id="${item.id}">
              <i class="bi bi-trash"></i>
            </button>
          </div>
        </div>
        <div class="mt-1">
          <div class="input-group input-group-sm">
            <button class="btn btn-outline-secondary decrease-qty" data-id="${item.id}">-</button>
            <input type="number" class="form-control text-center item-qty" value="${item.quantity}" min="1" data-id="${item.id}">
            <button class="btn btn-outline-secondary increase-qty" data-id="${item.id}">+</button>
          </div>
        </div>
      `;
      cartContainer.appendChild(itemElement);
    });
    
    // Ajouter les gestionnaires d'événements pour les boutons du panier
    this.addCartEventListeners();
  },
  
  // Ajouter les gestionnaires d'événements pour les boutons du panier
  addCartEventListeners: function() {
    // Gestionnaire pour supprimer un article
    document.querySelectorAll('.remove-item').forEach(button => {
      button.addEventListener('click', (e) => {
        const productId = parseInt(e.currentTarget.getAttribute('data-id'));
        this.removeProduct(productId);
      });
    });
    
    // Gestionnaire pour diminuer la quantité
    document.querySelectorAll('.decrease-qty').forEach(button => {
      button.addEventListener('click', (e) => {
        const productId = parseInt(e.currentTarget.getAttribute('data-id'));
        const item = this.currentSale.items.find(item => item.id === productId);
        if (item && item.quantity > 1) {
          this.updateQuantity(productId, item.quantity - 1);
        }
      });
    });
    
    // Gestionnaire pour augmenter la quantité
    document.querySelectorAll('.increase-qty').forEach(button => {
      button.addEventListener('click', (e) => {
        const productId = parseInt(e.currentTarget.getAttribute('data-id'));
        const item = this.currentSale.items.find(item => item.id === productId);
        if (item) {
          this.updateQuantity(productId, item.quantity + 1);
        }
      });
    });
    
    // Gestionnaire pour modifier directement la quantité
    document.querySelectorAll('.item-qty').forEach(input => {
      input.addEventListener('change', (e) => {
        const productId = parseInt(e.currentTarget.getAttribute('data-id'));
        const quantity = parseInt(e.currentTarget.value);
        if (quantity > 0) {
          this.updateQuantity(productId, quantity);
        } else {
          e.currentTarget.value = 1;
          this.updateQuantity(productId, 1);
        }
      });
    });
  },
  
  // Finaliser la vente
  checkout: function() {
    // Afficher le modal de paiement
    const paymentModal = new bootstrap.Modal(document.getElementById('paymentModal'));
    paymentModal.show();
    
    // Pré-remplir le montant reçu avec le total
    document.getElementById('amountReceived').value = this.currentSale.total.toFixed(2);
    this.updateChange();
  },
  
  // Mettre à jour le montant à rendre
  updateChange: function() {
    const amountReceived = parseFloat(document.getElementById('amountReceived').value) || 0;
    const changeAmount = amountReceived - this.currentSale.total;
    
    document.getElementById('changeAmount').value = changeAmount >= 0 ? changeAmount.toFixed(2) : '0.00';
  },
  
  // Confirmer le paiement
  confirmPayment: function() {
    // Générer un ticket de caisse
    this.generateReceipt();
    
    // Enregistrer la vente dans la base de données
    this.saveSale();
    
    // Afficher le modal du ticket
    const receiptModal = new bootstrap.Modal(document.getElementById('receiptModal'));
    receiptModal.show();
    
    // Fermer le modal de paiement
    const paymentModal = document.getElementById('paymentModal');
    bootstrap.Modal.getInstance(paymentModal).hide();
  },
  
  // Générer le ticket de caisse
  generateReceipt: function() {
    const receiptContent = document.getElementById('receiptContent');
    const clientName = document.getElementById('posClient').options[document.getElementById('posClient').selectedIndex].text;
    const date = new Date().toLocaleString();
    
    let receiptHTML = `
      <div class="text-center mb-3">
        <h4>GestiCom</h4>
        <p class="mb-0">123 Rue du Commerce</p>
        <p class="mb-0">Alger, Algérie</p>
        <p class="mb-0">Tel: 021 23 45 67</p>
      </div>
      <div class="mb-3">
        <p class="mb-0"><strong>Date:</strong> ${date}</p>
        <p class="mb-0"><strong>Client:</strong> ${clientName}</p>
        <p class="mb-0"><strong>Mode de paiement:</strong> ${this.getPaymentMethodName()}</p>
      </div>
      <table class="table table-sm">
        <thead>
          <tr>
            <th>Produit</th>
            <th class="text-end">Prix</th>
            <th class="text-end">Qté</th>
            <th class="text-end">Total</th>
          </tr>
        </thead>
        <tbody>
    `;
    
    this.currentSale.items.forEach(item => {
      receiptHTML += `
        <tr>
          <td>${item.name}</td>
          <td class="text-end">${item.price.toFixed(2)} DA</td>
          <td class="text-end">${item.quantity}</td>
          <td class="text-end">${item.total.toFixed(2)} DA</td>
        </tr>
      `;
    });
    
    receiptHTML += `
        </tbody>
      </table>
      <div class="d-flex justify-content-between mb-1">
        <span>Total HT:</span>
        <span>${this.currentSale.subtotal.toFixed(2)} DA</span>
      </div>
      <div class="d-flex justify-content-between mb-1">
        <span>TVA (19%):</span>
        <span>${this.currentSale.tax.toFixed(2)} DA</span>
      </div>
      <div class="d-flex justify-content-between mb-1">
        <span><strong>Total TTC:</strong></span>
        <span><strong>${this.currentSale.total.toFixed(2)} DA</strong></span>
      </div>
      <div class="d-flex justify-content-between mb-1">
        <span>Montant reçu:</span>
        <span>${parseFloat(document.getElementById('amountReceived').value).toFixed(2)} DA</span>
      </div>
      <div class="d-flex justify-content-between mb-1">
        <span>Monnaie rendue:</span>
        <span>${parseFloat(document.getElementById('changeAmount').value).toFixed(2)} DA</span>
      </div>
      <div class="text-center mt-3">
        <p>Merci de votre visite!</p>
      </div>
    `;
    
    receiptContent.innerHTML = receiptHTML;
  },
  
  // Obtenir le nom du mode de paiement
  getPaymentMethodName: function() {
    const methods = {
      'cash': 'Espèces',
      'card': 'Carte bancaire',
      'check': 'Chèque'
    };
    return methods[this.currentSale.paymentMethod] || 'Inconnu';
  },
  
  // Enregistrer la vente dans la base de données
  saveSale: function() {
    // Simuler l'enregistrement de la vente
    console.log('Vente enregistrée:', this.currentSale);
    
    // Mettre à jour le stock des produits vendus
    this.updateProductStock();
    
    // Réinitialiser la vente courante
    this.resetSale();
  },
  
  // Mettre à jour le stock des produits vendus
  updateProductStock: function() {
    // Pour chaque article vendu, mettre à jour le stock
    this.currentSale.items.forEach(item => {
      // Simuler la mise à jour du stock
      console.log(`Stock mis à jour pour le produit ${item.id}: -${item.quantity}`);
    });
  },
  
  // Imprimer le ticket
  printReceipt: function() {
    const receiptContent = document.getElementById('receiptContent').innerHTML;
    const printWindow = window.open('', '_blank');
    
    printWindow.document.write(`
      <html>
        <head>
          <title>Ticket de caisse</title>
          <style>
            body { font-family: Arial, sans-serif; font-size: 12px; }
            .text-center { text-align: center; }
            .text-end { text-align: right; }
            .mb-0 { margin-bottom: 0; }
            .mb-1 { margin-bottom: 0.25rem; }
            .mb-3 { margin-bottom: 1rem; }
            .mt-4 { margin-top: 1.5rem; }
            .fw-bold { font-weight: bold; }
            .d-flex { display: flex; }
            .justify-content-between { justify-content: space-between; }
            table { width: 100%; border-collapse: collapse; }
            th, td { padding: 0.25rem; }
          </style>
        </head>
        <body>
          ${receiptContent}
        </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  }
};

// Fonction d'initialisation pour la section caisse (POS)
function initPOSSection() {
  console.log("Initialisation de la section caisse");
  
  // Charger les produits
  loadPOSProducts();
  
  // Réinitialiser la vente courante
  pos.resetSale();
  
  // Ajouter les gestionnaires d'événements
  document.getElementById('newSaleBtn')?.addEventListener('click', () => pos.resetSale());
  document.getElementById('posCheckoutBtn')?.addEventListener('click', () => pos.checkout());
  document.getElementById('posClearBtn')?.addEventListener('click', () => pos.resetSale());
  
  const paymentMethodSelect = document.getElementById('posPaymentMethod');
  if (paymentMethodSelect) {
    paymentMethodSelect.addEventListener('change', (e) => {
      pos.currentSale.paymentMethod = e.target.value;
    });
  }
  
  document.getElementById('amountReceived')?.addEventListener('input', () => pos.updateChange());
  document.getElementById('confirmPaymentBtn')?.addEventListener('click', () => pos.confirmPayment());
  document.getElementById('printReceiptBtn')?.addEventListener('click', () => pos.printReceipt());
  document.getElementById('searchPosProduct')?.addEventListener('input', filterPOSProducts);
  
  // Charger les clients pour le sélecteur
  loadPOSClients();
}

// Fonction pour charger les produits dans la grille
async function loadPOSProducts() {
  try {
    const products = await productManager.getAll();
    const productsGrid = document.getElementById('posProductsGrid');
    
    if (!productsGrid) {
      console.error("Élément posProductsGrid non trouvé");
      return;
    }
    
    if (products.length === 0) {
      productsGrid.innerHTML = '<div class="col-12 text-center">Aucun produit disponible</div>';
      return;
    }
    
    productsGrid.innerHTML = '';
    products.forEach(product => {
      const productCard = document.createElement('div');
      productCard.className = 'col-md-4 col-lg-3 mb-3';
      productCard.innerHTML = `
        <div class="card h-100 ${product.stockQuantity === 0 ? 'border-danger' : ''}">
          <div class="card-body">
            <h6 class="card-title">${product.name}</h6>
            <p class="card-text mb-1">${product.price.toFixed(2)} DA</p>
            <p class="card-text ${product.stockQuantity === 0 ? 'text-danger' : ''}">
              Stock: ${product.stockQuantity}
            </p>
            <button class="btn btn-sm btn-primary add-to-cart" data-id="${product.id}" 
              ${product.stockQuantity === 0 ? 'disabled' : ''}>
              <i class="bi bi-cart-plus"></i> Ajouter
            </button>
          </div>
        </div>
      `;
      productsGrid.appendChild(productCard);
    });
    
    // Ajouter les gestionnaires d'événements pour les boutons d'ajout au panier
    document.querySelectorAll('.add-to-cart').forEach(button => {
      button.addEventListener('click', async (e) => {
        const productId = parseInt(e.currentTarget.getAttribute('data-id'));
        try {
          const product = await productManager.getById(productId);
          pos.addProduct(product);
        } catch (error) {
          console.error('Erreur lors de l\'ajout du produit au panier:', error);
        }
      });
    });
  } catch (error) {
    console.error('Erreur lors du chargement des produits:', error);
    const productsGrid = document.getElementById('posProductsGrid');
    if (productsGrid) {
      productsGrid.innerHTML = 
        '<div class="col-12 text-center text-danger">Erreur lors du chargement des produits</div>';
    }
  }
}

// Fonction pour filtrer les produits dans la grille
function filterPOSProducts() {
  const searchTerm = document.getElementById('searchPosProduct')?.value.toLowerCase() || '';
  const productCards = document.querySelectorAll('#posProductsGrid .card');
  
  productCards.forEach(card => {
    const productName = card.querySelector('.card-title')?.textContent.toLowerCase() || '';
    const productContainer = card.parentElement;
    if (productContainer) {
      productContainer.style.display = productName.includes(searchTerm) ? '' : 'none';
    }
  });
}

// Fonction pour charger les clients dans le sélecteur
async function loadPOSClients() {
  try {
    const clients = await clientManager.getAll();
    const clientSelect = document.getElementById('posClient');
    
    if (!clientSelect) {
      console.error("Élément posClient non trouvé");
      return;
    }
    
    // Conserver l'option par défaut
    clientSelect.innerHTML = '<option value="">Client occasionnel</option>';
    
    clients.forEach(client => {
      const option = document.createElement('option');
      option.value = client.id;
      option.textContent = client.name;
      clientSelect.appendChild(option);
    });
    
    // Ajouter le gestionnaire d'événement pour le changement de client
    clientSelect.addEventListener('change', (e) => {
      pos.currentSale.client = e.target.value ? parseInt(e.target.value) : null;
    });
  } catch (error) {
    console.error('Erreur lors du chargement des clients:', error);
  }
}










