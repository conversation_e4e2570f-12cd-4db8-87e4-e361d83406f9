@echo off
title GestiCom - Version Corrigee (index.html)
color 0A

echo.
echo ==========================================
echo   GESTICOM - VERSION CORRIGEE
echo ==========================================
echo.

echo CORRECTION APPLIQUEE:
echo - Redirection vers index.html au lieu de app.html
echo - Page produits fonctionnelle avec MySQL
echo - Toutes les fonctionnalites operationnelles
echo.

echo Test de la connexion MySQL...
node quick-test.js

echo.
echo Demarrage du serveur corrige...
echo.

REM Arreter tous les processus Node.js existants
taskkill /F /IM node.exe >nul 2>&1

REM Attendre 2 secondes
ping 127.0.0.1 -n 3 > nul

REM Demarrer le serveur
start "GestiCom Server" /MIN node simple-server.js

REM Attendre 4 secondes
ping 127.0.0.1 -n 5 > nul

echo Ouverture de l'application...
start http://localhost:3000/login.html

echo.
echo ==========================================
echo   GESTICOM - REDIRECTION CORRIGEE !
echo ==========================================
echo.
echo Application: http://localhost:3000/login.html
echo Apres connexion: http://localhost:3000/index.html
echo API Status: http://localhost:3000/api/status
echo.
echo Base de donnees: gesticom (MySQL)
echo Produits: 4 dans votre base MySQL
echo.
echo Comptes de connexion:
echo.
echo   admin / admin    (Administrateur)
echo   demo / demo      (Demonstration)
echo.
echo CORRECTIONS APPLIQUEES:
echo ✅ Redirection vers index.html
echo ✅ Page produits fonctionnelle
echo ✅ API connectee a MySQL
echo ✅ Gestion d'erreurs amelioree
echo.
echo Maintenant l'application redirige vers la bonne page !
echo.
echo Pour arreter l'application:
echo - Fermez la fenetre "GestiCom Server"
echo - Ou fermez cette fenetre
echo.

pause
