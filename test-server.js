const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const port = 3000;

console.log('🚀 Démarrage du serveur de test...');

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('./'));

// Données de test
const testData = {
  products: [
    { id: 1, name: "Ordinateur portable", description: "PC portable haute performance", price: 89999.99, stockQuantity: 15, category: "Informatique", barcode: "123456789", sku: "ORD-001", minStockLevel: 5 },
    { id: 2, name: "Smartphone", description: "Téléphone intelligent", price: 49999.99, stockQuantity: 25, category: "Téléphonie", barcode: "987654321", sku: "TEL-001", minStockLevel: 10 },
    { id: 3, name: "Écran 24 pouces", description: "Moniteur LED 24 pouces", price: 19999.99, stockQuantity: 8, category: "Informatique", barcode: "456789123", sku: "ECR-001", minStockLevel: 3 },
    { id: 4, name: "Souris sans fil", description: "Souris optique sans fil", price: 2999.99, stockQuantity: 50, category: "Accessoires", barcode: "789123456", sku: "SOU-001", minStockLevel: 15 }
  ],
  clients: [
    { id: 1, name: "Dupont Entreprise", contactPerson: "Jean Dupont", email: "<EMAIL>", phone: "01 23 45 67 89", address: "123 Rue de Paris", city: "Paris", postalCode: "75001", country: "France" },
    { id: 2, name: "Martin SARL", contactPerson: "Marie Martin", email: "<EMAIL>", phone: "01 98 76 54 32", address: "45 Avenue des Champs", city: "Paris", postalCode: "75008", country: "France" },
    { id: 3, name: "Petit Commerce", contactPerson: "Pierre Petit", email: "<EMAIL>", phone: "01 45 67 89 10", address: "78 Boulevard Haussmann", city: "Paris", postalCode: "75009", country: "France" }
  ]
};

// Routes API de test
app.get('/api/products', (req, res) => {
  console.log('📦 GET /api/products');
  res.json(testData.products);
});

app.get('/api/clients', (req, res) => {
  console.log('👥 GET /api/clients');
  res.json(testData.clients);
});

app.get('/api/suppliers', (req, res) => {
  console.log('🏢 GET /api/suppliers');
  res.json([
    { id: 1, name: "Tech Supplies Inc.", email: "<EMAIL>", phone: "01 23 45 67 89", address: "123 Tech Street, 75001 Paris" },
    { id: 2, name: "Global Electronics", email: "<EMAIL>", phone: "01 98 76 54 32", address: "45 Electronics Avenue, 75008 Paris" }
  ]);
});

app.get('/api/invoices', (req, res) => {
  console.log('🧾 GET /api/invoices');
  res.json([
    { id: 1, invoiceNumber: "FACT-001", date: "2024-01-15", clientName: "Dupont Entreprise", total: 150000, status: "paid" },
    { id: 2, invoiceNumber: "FACT-002", date: "2024-01-16", clientName: "Martin SARL", total: 75000, status: "unpaid" }
  ]);
});

// Route de test
app.get('/api/test', (req, res) => {
  console.log('🧪 GET /api/test');
  res.json({ 
    message: 'Serveur de test fonctionnel', 
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route pour servir l'application
app.get('/', (req, res) => {
  console.log('🏠 GET /');
  res.sendFile(path.join(__dirname, 'app.html'));
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({ error: 'Erreur interne du serveur' });
});

// Démarrage du serveur
app.listen(port, () => {
  console.log('✅ Serveur de test démarré avec succès !');
  console.log(`📍 URL: http://localhost:${port}`);
  console.log(`🌐 Application: http://localhost:${port}/app.html`);
  console.log(`🔐 Connexion: http://localhost:${port}/login.html`);
  console.log(`🧪 Test API: http://localhost:${port}/api/test`);
  console.log('\n💡 Pour arrêter le serveur, appuyez sur Ctrl+C');
});

// Gestion de l'arrêt propre
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt du serveur...');
  process.exit(0);
});
