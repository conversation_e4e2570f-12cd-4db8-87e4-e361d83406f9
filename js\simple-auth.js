// ===== AUTHENTIFICATION SIMPLIFIÉE POUR GESTICOM =====

document.addEventListener('DOMContentLoaded', function() {
  console.log('🔐 Initialisation de l\'authentification...');

  // Vérifier si on est sur la page de connexion
  if (window.location.pathname.includes('login.html')) {
    initLoginPage();
  } else {
    // Vérifier l'authentification pour les autres pages
    checkAuthentication();
  }
});

// ===== PAGE DE CONNEXION =====
function initLoginPage() {
  console.log('📝 Initialisation de la page de connexion');

  const loginForm = document.getElementById('loginForm');
  if (loginForm) {
    loginForm.addEventListener('submit', handleLogin);
  }

  // Ajouter un bouton de connexion démo
  addDemoLoginButton();
}

function addDemoLoginButton() {
  const loginForm = document.getElementById('loginForm');
  if (loginForm) {
    const demoButton = document.createElement('button');
    demoButton.type = 'button';
    demoButton.className = 'btn btn-outline-primary w-100 mt-2';
    demoButton.innerHTML = '<i class="bi bi-play-circle me-2"></i>Connexion Démo';
    demoButton.addEventListener('click', loginDemo);

    loginForm.appendChild(demoButton);
  }
}

async function handleLogin(event) {
  event.preventDefault();

  const formData = new FormData(event.target);
  const credentials = {
    username: formData.get('username'),
    password: formData.get('password')
  };

  console.log('🔐 Tentative de connexion pour:', credentials.username);

  try {
    // Afficher un indicateur de chargement
    showLoginLoading(true);

    // Simuler une authentification (à remplacer par un vrai appel API)
    await simulateLogin(credentials);

    // Connexion réussie
    console.log('✅ Connexion réussie');
    showNotification('Connexion réussie', 'success');

    // Rediriger vers l'application
    window.location.replace('app.html');

  } catch (error) {
    console.error('❌ Erreur de connexion:', error);
    showNotification('Erreur de connexion', 'error');
    showLoginLoading(false);
  }
}

function loginDemo() {
  console.log('🎭 Connexion en mode démo');

  // Créer un token de démo
  const demoToken = 'demo-token-' + Date.now();
  localStorage.setItem('authToken', demoToken);
  localStorage.setItem('userInfo', JSON.stringify({
    id: 1,
    username: 'demo',
    name: 'Utilisateur Démo',
    role: 'admin'
  }));

  showNotification('Connexion démo activée', 'success');

  // Rediriger vers l'application
  setTimeout(() => {
    window.location.replace('app.html');
  }, 1000);
}

async function simulateLogin(credentials) {
  // Simuler un délai de connexion
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Vérifications simples
  if (!credentials.username || !credentials.password) {
    throw new Error('Nom d\'utilisateur et mot de passe requis');
  }

  // Comptes de test prédéfinis
  const validAccounts = {
    'admin': 'admin',
    'demo': 'demo',
    'user': 'password',
    'test': 'test'
  };

  // Vérifier les identifiants
  if (!validAccounts[credentials.username] || validAccounts[credentials.username] !== credentials.password) {
    throw new Error('Nom d\'utilisateur ou mot de passe incorrect');
  }

  // Créer un token simulé
  const token = 'auth-token-' + Date.now();
  localStorage.setItem('authToken', token);
  localStorage.setItem('userInfo', JSON.stringify({
    id: 1,
    username: credentials.username,
    name: credentials.username === 'admin' ? 'Administrateur' : credentials.username,
    role: credentials.username === 'admin' ? 'admin' : 'user'
  }));

  return { token, user: { username: credentials.username } };
}

function showLoginLoading(show) {
  const submitButton = document.querySelector('#loginForm button[type="submit"]');
  if (submitButton) {
    if (show) {
      submitButton.disabled = true;
      submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Connexion...';
    } else {
      submitButton.disabled = false;
      submitButton.innerHTML = '<i class="bi bi-box-arrow-in-right me-2"></i>Se connecter';
    }
  }
}

// ===== VÉRIFICATION D'AUTHENTIFICATION =====
function checkAuthentication() {
  const token = localStorage.getItem('authToken');

  if (!token) {
    console.log('❌ Aucun token d\'authentification trouvé');
    redirectToLogin();
    return false;
  }

  console.log('✅ Token d\'authentification trouvé');
  return true;
}

function redirectToLogin() {
  console.log('🔄 Redirection vers la page de connexion');
  window.location.replace('login.html');
}

// ===== DÉCONNEXION =====
function logout() {
  console.log('🚪 Déconnexion...');

  // Supprimer les données d'authentification
  localStorage.removeItem('authToken');
  localStorage.removeItem('userInfo');

  // Afficher une notification
  showNotification('Déconnexion réussie', 'info');

  // Rediriger vers la page de connexion
  setTimeout(() => {
    redirectToLogin();
  }, 1000);
}

// ===== UTILITAIRES =====
function showNotification(message, type = 'info') {
  // Créer une notification simple
  const notification = document.createElement('div');
  notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
  notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
  notification.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;

  document.body.appendChild(notification);

  // Supprimer automatiquement après 3 secondes
  setTimeout(() => {
    if (notification.parentNode) {
      notification.remove();
    }
  }, 3000);
}

function getUserInfo() {
  const userInfo = localStorage.getItem('userInfo');
  return userInfo ? JSON.parse(userInfo) : null;
}

function isAuthenticated() {
  return localStorage.getItem('authToken') !== null;
}

// ===== EXPORTS GLOBAUX =====
window.logout = logout;
window.getUserInfo = getUserInfo;
window.isAuthenticated = isAuthenticated;
window.showNotification = showNotification;

console.log('🔐 Module d\'authentification chargé');
