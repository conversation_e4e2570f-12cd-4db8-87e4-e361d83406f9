@echo off
title GestiCom - Version Finale avec MySQL
color 0A

echo.
echo ==========================================
echo   GESTICOM - VERSION FINALE AVEC MYSQL
echo ==========================================
echo.

echo Test de la connexion MySQL...
node quick-test.js

echo.
echo Demarrage du serveur final...
echo.

REM Arreter tous les processus Node.js existants
taskkill /F /IM node.exe >nul 2>&1

REM Attendre 2 secondes
ping 127.0.0.1 -n 3 > nul

REM Demarrer le serveur final
start "GestiCom MySQL Server" /MIN node simple-server.js

REM Attendre 4 secondes
ping 127.0.0.1 -n 5 > nul

echo Ouverture de l'application...
start http://localhost:3000/login.html

echo.
echo ==========================================
echo   GESTICOM FINAL - CONNECTE A MYSQL !
echo ==========================================
echo.
echo Application: http://localhost:3000/login.html
echo Test Produits: http://localhost:3000/test-products-fix.html
echo API Status: http://localhost:3000/api/status
echo.
echo Base de donnees: gesticom (MySQL)
echo Produits: 4 dans votre base MySQL
echo Clients: 3 dans votre base MySQL
echo Fournisseurs: 3 dans votre base MySQL
echo.
echo Comptes de connexion:
echo.
echo   admin / admin    (Administrateur)
echo.
echo TOUTES LES CORRECTIONS APPLIQUEES:
echo - API connectee a MySQL
echo - Structure de base adaptee
echo - Page produits corrigee
echo - Logs detailles ajoutes
echo - Gestion d'erreurs amelioree
echo.
echo L'application utilise VOS VRAIES DONNEES MySQL !
echo.
echo Pour arreter l'application:
echo - Fermez la fenetre "GestiCom MySQL Server"
echo - Ou fermez cette fenetre
echo.

pause
