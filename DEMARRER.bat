@echo off
title GestiCom - Application de Gestion Commerciale
color 0B

echo.
echo ==========================================
echo    GESTICOM - DEMARRAGE AUTOMATIQUE
echo ==========================================
echo.

echo Demarrage du serveur GestiCom...
echo.

REM Demarrer le serveur
start "Serveur GestiCom" /MIN node simple-server.js

REM Attendre 4 secondes
ping 127.0.0.1 -n 5 > nul

echo Ouverture de l'application...
start http://localhost:3000/login.html

echo.
echo ==========================================
echo     GESTICOM EST MAINTENANT ACTIF !
echo ==========================================
echo.
echo Application: http://localhost:3000/login.html
echo.
echo Comptes de demonstration:
echo.
echo   admin / admin    (Administrateur)
echo   demo / demo      (Demonstration)
echo.
echo Pour arreter l'application:
echo - Fermez la fenetre "Serveur GestiCom"
echo - Ou fermez cette fenetre
echo.

pause
