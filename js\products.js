// Le productManager est maintenant défini dans utils.js
console.log('📦 products.js - Chargement du module produits...');

// Objet pour gérer les catégories
const categoryManager = {
  getAll: async () => {
    try {
      const response = await fetch('/api/categories');
      return await response.json();
    } catch (error) {
      console.log("API non disponible, utilisation des données de test");
      // Retourner des données de test si l'API échoue
      return [
        { id: 1, name: "Informatique" },
        { id: 2, name: "Téléphonie" },
        { id: 3, name: "Accessoires" },
        { id: 4, name: "Bureautique" }
      ];
    }
  },
  getById: async (id) => {
    try {
      const response = await fetch(`/api/categories/${id}`);
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de récupération");
      // Simuler la récupération d'une catégorie
      const testCategories = [
        { id: 1, name: "Informatique" },
        { id: 2, name: "<PERSON>éléphonie" },
        { id: 3, name: "Accessoires" },
        { id: 4, name: "Bureautique" }
      ];
      return testCategories.find(c => c.id === parseInt(id)) || null;
    }
  },
  add: async (category) => {
    try {
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(category)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation d'ajout");
      return { ...category, id: Math.floor(Math.random() * 1000) + 10 };
    }
  },
  update: async (id, data) => {
    try {
      const response = await fetch(`/api/categories/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(data)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de mise à jour");
      return { ...data, id: parseInt(id) };
    }
  },
  delete: async (id) => {
    try {
      await fetch(`/api/categories/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${localStorage.getItem('authToken')}` }
      });
      return true;
    } catch (error) {
      console.log("API non disponible, simulation de suppression");
      return true;
    }
  }
};

// Fonction d'initialisation pour la section produits
function initProductsSection() {
  console.log('🔧 Initialisation de la section produits...');

  // Attendre que le DOM soit prêt
  setTimeout(() => {
    console.log('📦 Chargement de la liste des produits...');

    // Vérifier que l'élément existe
    const tableBody = document.getElementById('productsTableBody');
    if (!tableBody) {
      console.error('❌ Élément productsTableBody non trouvé');
      return;
    }

    // Charger la liste des produits
    loadProductsList();

    // Charger les catégories dans le sélecteur
    loadCategoriesForSelect();

    // Ajouter les gestionnaires d'événements
    document.getElementById('saveProductBtn')?.addEventListener('click', saveProduct);
    document.getElementById('searchProduct')?.addEventListener('input', filterProducts);

    // Gestionnaires pour les catégories
    document.getElementById('addCategoryBtn')?.addEventListener('click', addCategory);
    document.getElementById('saveCategoryBtn')?.addEventListener('click', saveCategory);

    // Gestionnaire pour l'ouverture du modal des catégories
    document.getElementById('manageCategoriesModal')?.addEventListener('show.bs.modal', loadCategoriesList);

    console.log('✅ Section produits initialisée');
  }, 100);
}

// Fonction pour charger les catégories dans le sélecteur
async function loadCategoriesForSelect() {
  try {
    const categories = await categoryManager.getAll();
    const categorySelect = document.getElementById('productCategory');

    if (categorySelect) {
      // Conserver l'option par défaut
      categorySelect.innerHTML = '<option value="">Sélectionner une catégorie</option>';

      categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        categorySelect.appendChild(option);
      });
    }
  } catch (error) {
    console.error('Erreur lors du chargement des catégories:', error);
  }
}

// Fonction pour charger la liste des catégories
async function loadCategoriesList() {
  try {
    const categories = await categoryManager.getAll();
    const tableBody = document.getElementById('categoriesTableBody');

    if (tableBody) {
      if (categories.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="2" class="text-center">Aucune catégorie trouvée</td></tr>';
        return;
      }

      tableBody.innerHTML = '';
      categories.forEach(category => {
        tableBody.innerHTML += `
          <tr>
            <td>${category.name}</td>
            <td>
              <button class="btn btn-sm btn-outline-primary edit-category" data-id="${category.id}">
                <i class="bi bi-pencil"></i>
              </button>
              <button class="btn btn-sm btn-outline-danger delete-category" data-id="${category.id}">
                <i class="bi bi-trash"></i>
              </button>
            </td>
          </tr>
        `;
      });

      // Ajouter les gestionnaires d'événements pour les boutons d'édition et de suppression
      addCategoryActionHandlers();
    }
  } catch (error) {
    console.error('Erreur lors du chargement des catégories:', error);
    if (document.getElementById('categoriesTableBody')) {
      document.getElementById('categoriesTableBody').innerHTML =
        '<tr><td colspan="2" class="text-center text-danger">Erreur lors du chargement des catégories</td></tr>';
    }
  }
}

// Fonction pour ajouter les gestionnaires d'événements aux boutons d'action des catégories
function addCategoryActionHandlers() {
  // Gestionnaires pour les boutons d'édition
  document.querySelectorAll('.edit-category').forEach(button => {
    button.addEventListener('click', async (e) => {
      const categoryId = e.currentTarget.getAttribute('data-id');
      try {
        const category = await categoryManager.getById(categoryId);

        // Remplir le formulaire avec les données de la catégorie
        document.getElementById('categoryId').value = category.id;
        document.getElementById('categoryName').value = category.name;

        // Ouvrir le modal
        const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
        modal.show();
      } catch (error) {
        console.error('Erreur lors de la récupération de la catégorie:', error);
        alert('Erreur lors de la récupération de la catégorie');
      }
    });
  });

  // Gestionnaires pour les boutons de suppression
  document.querySelectorAll('.delete-category').forEach(button => {
    button.addEventListener('click', async (e) => {
      const categoryId = e.currentTarget.getAttribute('data-id');
      if (confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?')) {
        try {
          await categoryManager.delete(categoryId);
          loadCategoriesList();
        } catch (error) {
          console.error('Erreur lors de la suppression de la catégorie:', error);
          showError('Erreur lors de la suppression de la catégorie: ' + error.message);
        }
      }
    });
  });
}

// Fonction pour ajouter une nouvelle catégorie
async function addCategory() {
  const categoryName = document.getElementById('newCategoryName').value.trim();

  if (!categoryName) {
    alert('Veuillez saisir un nom de catégorie');
    return;
  }

  try {
    await categoryManager.add({ name: categoryName });
    document.getElementById('newCategoryName').value = '';
    loadCategoriesList();
  } catch (error) {
    console.error('Erreur lors de l\'ajout de la catégorie:', error);
    alert('Erreur lors de l\'ajout de la catégorie');
  }
}

// Fonction pour enregistrer les modifications d'une catégorie
async function saveCategory() {
  const categoryId = document.getElementById('categoryId').value;
  const categoryName = document.getElementById('categoryName').value.trim();

  if (!categoryName) {
    alert('Veuillez saisir un nom de catégorie');
    return;
  }

  try {
    await categoryManager.update(categoryId, { name: categoryName });

    // Fermer le modal et rafraîchir la liste
    const modal = bootstrap.Modal.getInstance(document.getElementById('editCategoryModal'));
    modal.hide();
    loadCategoriesList();
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la catégorie:', error);
    alert('Erreur lors de la mise à jour de la catégorie');
  }
}

// Fonction pour filtrer les produits selon la recherche
function filterProducts() {
  const searchTerm = document.getElementById('searchProduct').value.toLowerCase();
  const rows = document.querySelectorAll('#productsTableBody tr');

  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm) ? '' : 'none';
  });
}

// Fonction pour sauvegarder un produit (nouveau ou mise à jour)
async function saveProduct() {
  const productId = document.getElementById('productId')?.value;

  // Récupérer les valeurs du formulaire
  const name = document.getElementById('productName').value.trim();
  const price = parseFloat(document.getElementById('productPrice').value);
  const stockQuantity = parseInt(document.getElementById('productStock').value);
  const categoryId = document.getElementById('productCategory').value;

  // Validation basique
  if (!name) {
    showError('Le nom du produit est obligatoire');
    return;
  }

  if (isNaN(price) || price < 0) {
    showError('Le prix doit être un nombre positif');
    return;
  }

  if (isNaN(stockQuantity) || stockQuantity < 0) {
    showError('Le stock doit être un nombre positif');
    return;
  }

  const product = {
    name: name,
    description: '', // Champ vide pour l'instant
    price: price,
    stockQuantity: stockQuantity,
    categoryId: categoryId || null,
    barcode: null, // Champ vide pour l'instant
    sku: null, // Champ vide pour l'instant
    minStockLevel: 0 // Valeur par défaut
  };

  console.log('Données à envoyer:', product);

  try {
    let result;
    if (productId) {
      // Mise à jour d'un produit existant
      console.log(`Mise à jour du produit ${productId}`);
      result = await productManager.update(productId, product);
    } else {
      // Ajout d'un nouveau produit
      console.log('Ajout d\'un nouveau produit');
      result = await productManager.add(product);
    }

    console.log('Résultat:', result);

    // Fermer le modal et rafraîchir la liste
    const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
    modal.hide();

    // Réinitialiser le formulaire
    document.getElementById('addProductForm').reset();
    document.getElementById('productId').value = '';
    document.getElementById('addProductModalLabel').textContent = 'Ajouter un produit';

    // Rafraîchir la liste
    loadProductsList();

    // Afficher un message de succès
    showSuccess(productId ? 'Produit modifié avec succès' : 'Produit ajouté avec succès');

  } catch (error) {
    console.error('Erreur lors de l\'enregistrement du produit:', error);
    showError('Erreur lors de l\'enregistrement du produit: ' + error.message);
  }
}

// Fonction pour charger la liste des produits
async function loadProductsList() {
  console.log('📦 loadProductsList() - Début du chargement...');

  try {
    const tableBody = document.getElementById('productsTableBody');
    if (!tableBody) {
      console.error('❌ Élément productsTableBody non trouvé dans loadProductsList');
      return;
    }

    // Afficher un indicateur de chargement
    tableBody.innerHTML = '<tr><td colspan="6" class="text-center"><div class="spinner-border spinner-border-sm"></div> Chargement des produits...</td></tr>';

    console.log('🔄 Récupération des produits via API...');
    const products = await productManager.getAll();
    console.log(`✅ ${products.length} produits récupérés:`, products);

    if (products.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="6" class="text-center">Aucun produit trouvé</td></tr>';
      return;
    }

    tableBody.innerHTML = '';
    products.forEach(product => {
      // Adapter les noms de propriétés selon la structure de la base
      const stockQuantity = product.stockQuantity || product.stock_quantity || 0;
      const categoryName = product.category || product.category_name || '-';
      const price = parseFloat(product.price) || 0;

      console.log(`📦 Produit: ${product.name}, Stock: ${stockQuantity}, Prix: ${price}`);

      tableBody.innerHTML += `
        <tr>
          <td>${product.id}</td>
          <td>${product.name}</td>
          <td>${price.toFixed(2)} DA</td>
          <td>${stockQuantity}</td>
          <td>${categoryName}</td>
          <td>
            <button class="btn btn-sm btn-outline-primary edit-product" data-id="${product.id}">
              <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-product" data-id="${product.id}">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        </tr>
      `;
    });

    // Ajouter les gestionnaires d'événements pour les boutons d'édition et de suppression
    addProductActionHandlers();
    console.log('✅ Liste des produits chargée avec succès');

  } catch (error) {
    console.error('❌ Erreur lors du chargement des produits:', error);
    const tableBody = document.getElementById('productsTableBody');
    if (tableBody) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="6" class="text-center text-danger">
            Erreur lors du chargement des produits: ${error.message}
            <br><small>Vérifiez la console pour plus de détails</small>
          </td>
        </tr>
      `;
    }
  }
}

// Fonction pour ajouter les gestionnaires d'événements aux boutons d'action
function addProductActionHandlers() {
  // Gestionnaires pour les boutons d'édition
  document.querySelectorAll('.edit-product').forEach(button => {
    button.addEventListener('click', async (e) => {
      const productId = e.currentTarget.getAttribute('data-id');
      try {
        const product = await productManager.getById(productId);

        // Remplir le formulaire avec les données du produit
        // Adapter les noms de propriétés selon la structure de la base
        const stockQuantity = product.stockQuantity || product.stock_quantity || 0;
        const categoryId = product.categoryId || product.category_id || '';

        document.getElementById('productId').value = product.id;
        document.getElementById('productName').value = product.name || '';
        document.getElementById('productPrice').value = product.price || '';
        document.getElementById('productStock').value = stockQuantity;
        document.getElementById('productCategory').value = categoryId;

        // Changer le titre du modal
        document.getElementById('addProductModalLabel').textContent = 'Modifier un produit';

        // Ouvrir le modal
        const modal = new bootstrap.Modal(document.getElementById('addProductModal'));
        modal.show();
      } catch (error) {
        console.error('Erreur lors de la récupération du produit:', error);
        showError('Erreur lors de la récupération du produit: ' + error.message);
      }
    });
  });

  // Gestionnaires pour les boutons de suppression
  document.querySelectorAll('.delete-product').forEach(button => {
    button.addEventListener('click', async (e) => {
      const productId = e.currentTarget.getAttribute('data-id');
      const productName = e.currentTarget.closest('tr').querySelector('td:nth-child(2)').textContent;

      if (confirm(`Êtes-vous sûr de vouloir supprimer le produit "${productName}" ?`)) {
        try {
          console.log(`Suppression du produit ${productId}...`);
          await productManager.delete(productId);
          console.log('Produit supprimé avec succès');
          loadProductsList();
          showSuccess('Produit supprimé avec succès');
        } catch (error) {
          console.error('Erreur lors de la suppression du produit:', error);
          showError('Erreur lors de la suppression du produit: ' + error.message);
        }
      }
    });
  });
}





