// ===== SCRIPT DE DIAGNOSTIC POUR LE THÈME =====

class ThemeDiagnostic {
  constructor() {
    this.issues = [];
    this.fixes = [];
  }

  // Fonction principale de diagnostic
  runDiagnostic() {
    console.log('🔍 Démarrage du diagnostic du thème...');
    this.issues = [];
    this.fixes = [];

    this.checkThemeManager();
    this.checkDataThemeAttribute();
    this.checkTextColors();
    this.checkButtons();
    this.checkCSS();
    this.checkElements();

    this.reportResults();
    return this.issues.length === 0;
  }

  // Vérifier le gestionnaire de thème
  checkThemeManager() {
    if (!window.themeManager) {
      this.issues.push('ThemeManager non disponible');
      this.fixes.push('Vérifier que theme.js est chargé');
    } else {
      console.log('✅ ThemeManager disponible');
    }
  }

  // Vérifier l'attribut data-theme
  checkDataThemeAttribute() {
    const html = document.documentElement;
    const dataTheme = html.getAttribute('data-theme');

    if (window.themeManager) {
      const currentTheme = window.themeManager.getCurrentTheme();

      if (currentTheme === 'dark' && dataTheme !== 'dark') {
        this.issues.push(`Incohérence: thème=${currentTheme}, data-theme=${dataTheme}`);
        this.fixes.push('Forcer l\'application de l\'attribut data-theme');
        // Auto-fix
        html.setAttribute('data-theme', 'dark');
      } else if (currentTheme === 'light' && dataTheme === 'dark') {
        this.issues.push(`Incohérence: thème=${currentTheme}, data-theme=${dataTheme}`);
        this.fixes.push('Supprimer l\'attribut data-theme');
        // Auto-fix
        html.removeAttribute('data-theme');
      } else {
        console.log('✅ Attribut data-theme cohérent');
      }
    }
  }

  // Vérifier les couleurs de texte
  checkTextColors() {
    const isDark = document.documentElement.getAttribute('data-theme') === 'dark';

    if (isDark) {
      const problematicElements = [];
      const allElements = document.querySelectorAll('body, p, div, span, h1, h2, h3, h4, h5, h6, td, th, li, label');

      allElements.forEach(element => {
        const computedStyle = window.getComputedStyle(element);
        const color = computedStyle.color;

        if (color === 'rgb(0, 0, 0)' || color === '#000000' || color === 'rgb(33, 37, 41)') {
          problematicElements.push({
            element: element,
            tagName: element.tagName,
            className: element.className,
            color: color
          });
        }
      });

      if (problematicElements.length > 0) {
        this.issues.push(`${problematicElements.length} éléments avec du texte noir en mode sombre`);
        this.fixes.push('Appliquer forceWhiteTextInDarkMode()');

        // Auto-fix
        problematicElements.forEach(item => {
          item.element.style.setProperty('color', '#ffffff', 'important');
        });

        console.log('🔧 Correction automatique appliquée pour', problematicElements.length, 'éléments');
      } else {
        console.log('✅ Couleurs de texte correctes en mode sombre');
      }
    }
  }

  // Vérifier les boutons de thème
  checkButtons() {
    const sidebarButton = document.getElementById('sidebarThemeToggle');
    const floatingButton = document.getElementById('themeToggle');

    if (!sidebarButton) {
      this.issues.push('Bouton de thème sidebar non trouvé');
    } else {
      console.log('✅ Bouton sidebar trouvé');
    }

    if (!floatingButton) {
      console.log('ℹ️ Bouton flottant non trouvé (normal si pas créé)');
    } else {
      console.log('✅ Bouton flottant trouvé');
    }
  }

  // Vérifier le CSS
  checkCSS() {
    const themeCSS = document.querySelector('link[href*="themes.css"]');
    if (!themeCSS) {
      this.issues.push('Fichier themes.css non chargé');
      this.fixes.push('Vérifier le lien vers themes.css');
    } else {
      console.log('✅ Fichier themes.css chargé');
    }
  }

  // Vérifier des éléments spécifiques
  checkElements() {
    const isDark = document.documentElement.getAttribute('data-theme') === 'dark';

    if (isDark) {
      // Vérifier la sidebar
      const sidebar = document.querySelector('.sidebar');
      if (sidebar) {
        const sidebarBg = window.getComputedStyle(sidebar).backgroundColor;
        if (sidebarBg === 'rgb(248, 249, 250)') { // bg-light
          this.issues.push('Sidebar a encore un fond clair en mode sombre');
          this.fixes.push('Forcer le fond sombre de la sidebar');
        }
      }

      // Vérifier les cartes
      const cards = document.querySelectorAll('.card');
      cards.forEach(card => {
        const cardBg = window.getComputedStyle(card).backgroundColor;
        if (cardBg === 'rgb(255, 255, 255)') { // bg-white
          this.issues.push('Carte avec fond blanc en mode sombre');
          this.fixes.push('Forcer le fond sombre des cartes');
        }
      });
    }
  }

  // Rapporter les résultats
  reportResults() {
    console.log('\n📊 RÉSULTATS DU DIAGNOSTIC:');
    console.log('='.repeat(50));

    if (this.issues.length === 0) {
      console.log('✅ Aucun problème détecté!');
    } else {
      console.log(`❌ ${this.issues.length} problème(s) détecté(s):`);
      this.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
        if (this.fixes[index]) {
          console.log(`     💡 Solution: ${this.fixes[index]}`);
        }
      });
    }

    console.log('='.repeat(50));
  }

  // Fonction pour forcer la correction de tous les problèmes
  forceFixAll() {
    console.log('🔧 Application de toutes les corrections...');

    // Forcer l'attribut data-theme
    if (window.themeManager) {
      const currentTheme = window.themeManager.getCurrentTheme();
      if (currentTheme === 'dark') {
        document.documentElement.setAttribute('data-theme', 'dark');
      } else {
        document.documentElement.removeAttribute('data-theme');
      }
    }

    // Forcer les couleurs de texte
    if (window.forceWhiteTextInDarkMode) {
      window.forceWhiteTextInDarkMode();
    }

    // Forcer spécifiquement les cartes colorées
    if (window.forceColoredCardsText) {
      window.forceColoredCardsText();
    }

    // Forcer la mise à jour des boutons
    if (window.updateSidebarThemeButton) {
      window.updateSidebarThemeButton();
    }

    // Re-exécuter le diagnostic
    setTimeout(() => {
      this.runDiagnostic();
    }, 100);
  }
}

// Créer l'instance globale
window.themeDiagnostic = new ThemeDiagnostic();

// Fonction utilitaire pour exécuter le diagnostic
window.runThemeDiagnostic = function() {
  return window.themeDiagnostic.runDiagnostic();
};

// Fonction utilitaire pour forcer toutes les corrections
window.fixAllThemeIssues = function() {
  window.themeDiagnostic.forceFixAll();
};

console.log('🔍 Script de diagnostic du thème chargé');
console.log('💡 Utilisez runThemeDiagnostic() pour diagnostiquer');
console.log('💡 Utilisez fixAllThemeIssues() pour corriger automatiquement');
