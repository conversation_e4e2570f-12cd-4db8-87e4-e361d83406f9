// Le supplierManager est maintenant défini dans utils.js
console.log('🏢 suppliers.js - Chargement du module fournisseurs...');

// Vérifier que utils.js est chargé
if (typeof supplierManager === 'undefined') {
  console.error('❌ supplierManager non défini - utils.js doit être chargé avant suppliers.js');

  // Créer un supplierManager de fallback temporaire
  window.supplierManager = {
    getAll: async () => {
      console.warn('⚠️ Utilisation du supplierManager de fallback');
      const response = await fetch('/api/suppliers');
      return await response.json();
    },
    getById: async (id) => {
      const response = await fetch(`/api/suppliers/${id}`);
      return await response.json();
    },
    create: async (data) => {
      const response = await fetch('/api/suppliers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      return await response.json();
    },
    update: async (id, data) => {
      const response = await fetch(`/api/suppliers/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      return await response.json();
    },
    delete: async (id) => {
      const response = await fetch(`/api/suppliers/${id}`, {
        method: 'DELETE'
      });
      return await response.json();
    }
  };

  // Créer les fonctions de notification de fallback si nécessaire
  if (typeof showSuccess === 'undefined') {
    window.showSuccess = (message) => {
      console.log('✅ SUCCESS:', message);
      alert('Succès: ' + message);
    };
  }

  if (typeof showError === 'undefined') {
    window.showError = (message) => {
      console.error('❌ ERROR:', message);
      alert('Erreur: ' + message);
    };
  }

  console.log('✅ Fonctions de fallback créées pour suppliers');
} else {
  console.log('✅ supplierManager trouvé dans utils.js');
}

// Fonction d'initialisation pour la section fournisseurs
function initSuppliersSection() {
  // Charger la liste des fournisseurs
  loadSuppliersList();

  // Ajouter les gestionnaires d'événements
  document.getElementById('saveSupplierBtn')?.addEventListener('click', saveSupplier);
  document.getElementById('searchSupplier')?.addEventListener('input', filterSuppliers);
}

// Fonction pour charger la liste des fournisseurs
async function loadSuppliersList() {
  try {
    const suppliers = await supplierManager.getAll();
    const tableBody = document.getElementById('suppliersTableBody');

    if (suppliers.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="6" class="text-center">Aucun fournisseur trouvé</td></tr>';
      return;
    }

    tableBody.innerHTML = '';
    suppliers.forEach(supplier => {
      tableBody.innerHTML += `
        <tr>
          <td>${supplier.id}</td>
          <td>${supplier.name}</td>
          <td>${supplier.email}</td>
          <td>${supplier.phone || '-'}</td>
          <td>${supplier.address || '-'}</td>
          <td>
            <button class="btn btn-sm btn-outline-primary edit-supplier" data-id="${supplier.id}">
              <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-supplier" data-id="${supplier.id}">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        </tr>
      `;
    });

    // Ajouter les gestionnaires d'événements pour les boutons d'édition et de suppression
    addSupplierActionHandlers();
  } catch (error) {
    console.error('Erreur lors du chargement des fournisseurs:', error);
    document.getElementById('suppliersTableBody').innerHTML =
      '<tr><td colspan="6" class="text-center text-danger">Erreur lors du chargement des fournisseurs</td></tr>';
  }
}

// Fonction pour filtrer les fournisseurs selon la recherche
function filterSuppliers() {
  const searchTerm = document.getElementById('searchSupplier').value.toLowerCase();
  const rows = document.querySelectorAll('#suppliersTableBody tr');

  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm) ? '' : 'none';
  });
}

// Fonction pour sauvegarder un fournisseur (nouveau ou mise à jour)
async function saveSupplier() {
  const supplierId = document.getElementById('supplierId')?.value;

  // Récupérer les valeurs du formulaire
  const name = document.getElementById('supplierName').value.trim();
  const email = document.getElementById('supplierEmail').value.trim();
  const phone = document.getElementById('supplierPhone').value.trim();
  const address = document.getElementById('supplierAddress').value.trim();

  // Validation basique
  if (!name) {
    showError('Le nom du fournisseur est obligatoire');
    return;
  }

  if (!email) {
    showError('L\'email du fournisseur est obligatoire');
    return;
  }

  const supplier = {
    name: name,
    contactPerson: '', // Champ vide pour l'instant
    email: email,
    phone: phone || null,
    address: address || null,
    city: null, // Champ vide pour l'instant
    postalCode: null, // Champ vide pour l'instant
    country: 'Algérie', // Valeur par défaut
    taxId: null, // Champ vide pour l'instant
    notes: null // Champ vide pour l'instant
  };

  console.log('Données à envoyer:', supplier);

  try {
    let result;
    if (supplierId) {
      // Mise à jour d'un fournisseur existant
      console.log(`Mise à jour du fournisseur ${supplierId}`);
      result = await supplierManager.update(supplierId, supplier);
    } else {
      // Ajout d'un nouveau fournisseur
      console.log('Ajout d\'un nouveau fournisseur');
      result = await supplierManager.create(supplier);
    }

    console.log('Résultat:', result);

    // Fermer le modal et rafraîchir la liste
    const modal = bootstrap.Modal.getInstance(document.getElementById('addSupplierModal'));
    modal.hide();

    // Réinitialiser le formulaire
    document.getElementById('addSupplierForm').reset();
    document.getElementById('supplierId').value = '';
    document.getElementById('addSupplierModalLabel').textContent = 'Ajouter un fournisseur';

    // Rafraîchir la liste
    loadSuppliersList();

    // Afficher un message de succès
    showSuccess(supplierId ? 'Fournisseur modifié avec succès' : 'Fournisseur ajouté avec succès');

  } catch (error) {
    console.error('Erreur lors de l\'enregistrement du fournisseur:', error);
    showError('Erreur lors de l\'enregistrement du fournisseur: ' + error.message);
  }
}

// Fonction pour ajouter les gestionnaires d'événements aux boutons d'action
function addSupplierActionHandlers() {
  // Gestionnaires pour les boutons d'édition
  document.querySelectorAll('.edit-supplier').forEach(button => {
    button.addEventListener('click', async (e) => {
      const supplierId = e.currentTarget.getAttribute('data-id');
      try {
        const supplier = await supplierManager.getById(supplierId);

        // Remplir le formulaire avec les données du fournisseur
        document.getElementById('supplierId').value = supplier.id;
        document.getElementById('supplierName').value = supplier.name;
        document.getElementById('supplierEmail').value = supplier.email;
        document.getElementById('supplierPhone').value = supplier.phone || '';
        document.getElementById('supplierAddress').value = supplier.address || '';

        // Changer le titre du modal
        document.getElementById('addSupplierModalLabel').textContent = 'Modifier un fournisseur';

        // Ouvrir le modal
        const modal = new bootstrap.Modal(document.getElementById('addSupplierModal'));
        modal.show();
      } catch (error) {
        console.error('Erreur lors de la récupération du fournisseur:', error);
        showError('Erreur lors de la récupération du fournisseur: ' + error.message);
      }
    });
  });

  // Gestionnaires pour les boutons de suppression
  document.querySelectorAll('.delete-supplier').forEach(button => {
    button.addEventListener('click', async (e) => {
      const supplierId = e.currentTarget.getAttribute('data-id');
      if (confirm('Êtes-vous sûr de vouloir supprimer ce fournisseur ?')) {
        try {
          await supplierManager.delete(supplierId);
          loadSuppliersList();
          showSuccess('Fournisseur supprimé avec succès');
        } catch (error) {
          console.error('Erreur lors de la suppression du fournisseur:', error);
          showError('Erreur lors de la suppression du fournisseur: ' + error.message);
        }
      }
    });
  });
}

