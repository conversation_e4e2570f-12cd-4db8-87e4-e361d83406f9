const supplierManager = {
  getAll: async () => {
    try {
      const response = await fetch('/api/suppliers');
      return await response.json();
    } catch (error) {
      console.log("API non disponible, utilisation des données de test");
      // Retourner des données de test si l'API échoue
      return [
        { id: 1, name: "Tech Supplies Inc.", email: "<EMAIL>", phone: "01 23 45 67 89", address: "123 Tech Street, 75001 Paris" },
        { id: 2, name: "Global Electronics", email: "<EMAIL>", phone: "01 98 76 54 32", address: "45 Electronics Avenue, 75008 Paris" },
        { id: 3, name: "Office Solutions", email: "<EMAIL>", phone: "01 45 67 89 10", address: "78 Office Boulevard, 75009 Paris" }
      ];
    }
  },
  getById: async (id) => {
    try {
      const response = await fetch(`/api/suppliers/${id}`);
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de récupération");
      // Simuler la récupération d'un fournisseur
      const testSuppliers = [
        { id: 1, name: "Tech Supplies Inc.", email: "<EMAIL>", phone: "01 23 45 67 89", address: "123 Tech Street, 75001 Paris" },
        { id: 2, name: "Global Electronics", email: "<EMAIL>", phone: "01 98 76 54 32", address: "45 Electronics Avenue, 75008 Paris" },
        { id: 3, name: "Office Solutions", email: "<EMAIL>", phone: "01 45 67 89 10", address: "78 Office Boulevard, 75009 Paris" }
      ];
      return testSuppliers.find(s => s.id === parseInt(id)) || null;
    }
  },
  add: async (supplier) => {
    try {
      const response = await fetch('/api/suppliers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(supplier)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation d'ajout");
      return { ...supplier, id: Math.floor(Math.random() * 1000) + 10 };
    }
  },
  update: async (id, data) => {
    try {
      const response = await fetch(`/api/suppliers/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(data)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de mise à jour");
      return { ...data, id: parseInt(id) };
    }
  },
  delete: async (id) => {
    try {
      await fetch(`/api/suppliers/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${localStorage.getItem('authToken')}` }
      });
      return true;
    } catch (error) {
      console.log("API non disponible, simulation de suppression");
      return true;
    }
  },
  placeOrder: async (supplierId, products) => {
    // Passer une commande auprès d'un fournisseur
    try {
      const response = await fetch(`/api/suppliers/${supplierId}/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify({ products })
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de commande");
      return {
        id: Math.floor(Math.random() * 1000) + 1,
        supplierId: parseInt(supplierId),
        products,
        status: 'pending',
        date: new Date().toISOString()
      };
    }
  }
};

// Fonction d'initialisation pour la section fournisseurs
function initSuppliersSection() {
  // Charger la liste des fournisseurs
  loadSuppliersList();

  // Ajouter les gestionnaires d'événements
  document.getElementById('saveSupplierBtn')?.addEventListener('click', saveSupplier);
  document.getElementById('searchSupplier')?.addEventListener('input', filterSuppliers);
}

// Fonction pour charger la liste des fournisseurs
async function loadSuppliersList() {
  try {
    const suppliers = await supplierManager.getAll();
    const tableBody = document.getElementById('suppliersTableBody');

    if (suppliers.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="6" class="text-center">Aucun fournisseur trouvé</td></tr>';
      return;
    }

    tableBody.innerHTML = '';
    suppliers.forEach(supplier => {
      tableBody.innerHTML += `
        <tr>
          <td>${supplier.id}</td>
          <td>${supplier.name}</td>
          <td>${supplier.email}</td>
          <td>${supplier.phone || '-'}</td>
          <td>${supplier.address || '-'}</td>
          <td>
            <button class="btn btn-sm btn-outline-primary edit-supplier" data-id="${supplier.id}">
              <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-supplier" data-id="${supplier.id}">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        </tr>
      `;
    });

    // Ajouter les gestionnaires d'événements pour les boutons d'édition et de suppression
    addSupplierActionHandlers();
  } catch (error) {
    console.error('Erreur lors du chargement des fournisseurs:', error);
    document.getElementById('suppliersTableBody').innerHTML =
      '<tr><td colspan="6" class="text-center text-danger">Erreur lors du chargement des fournisseurs</td></tr>';
  }
}

// Fonction pour filtrer les fournisseurs selon la recherche
function filterSuppliers() {
  const searchTerm = document.getElementById('searchSupplier').value.toLowerCase();
  const rows = document.querySelectorAll('#suppliersTableBody tr');

  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm) ? '' : 'none';
  });
}

// Fonction pour sauvegarder un fournisseur (nouveau ou mise à jour)
async function saveSupplier() {
  const supplierId = document.getElementById('supplierId')?.value;

  // Récupérer les valeurs du formulaire
  const name = document.getElementById('supplierName').value.trim();
  const email = document.getElementById('supplierEmail').value.trim();
  const phone = document.getElementById('supplierPhone').value.trim();
  const address = document.getElementById('supplierAddress').value.trim();

  // Validation basique
  if (!name) {
    showError('Le nom du fournisseur est obligatoire');
    return;
  }

  if (!email) {
    showError('L\'email du fournisseur est obligatoire');
    return;
  }

  const supplier = {
    name: name,
    contactPerson: '', // Champ vide pour l'instant
    email: email,
    phone: phone || null,
    address: address || null,
    city: null, // Champ vide pour l'instant
    postalCode: null, // Champ vide pour l'instant
    country: 'Algérie', // Valeur par défaut
    taxId: null, // Champ vide pour l'instant
    notes: null // Champ vide pour l'instant
  };

  console.log('Données à envoyer:', supplier);

  try {
    let result;
    if (supplierId) {
      // Mise à jour d'un fournisseur existant
      console.log(`Mise à jour du fournisseur ${supplierId}`);
      result = await supplierManager.update(supplierId, supplier);
    } else {
      // Ajout d'un nouveau fournisseur
      console.log('Ajout d\'un nouveau fournisseur');
      result = await supplierManager.add(supplier);
    }

    console.log('Résultat:', result);

    // Fermer le modal et rafraîchir la liste
    const modal = bootstrap.Modal.getInstance(document.getElementById('addSupplierModal'));
    modal.hide();

    // Réinitialiser le formulaire
    document.getElementById('addSupplierForm').reset();
    document.getElementById('supplierId').value = '';
    document.getElementById('addSupplierModalLabel').textContent = 'Ajouter un fournisseur';

    // Rafraîchir la liste
    loadSuppliersList();

    // Afficher un message de succès
    showSuccess(supplierId ? 'Fournisseur modifié avec succès' : 'Fournisseur ajouté avec succès');

  } catch (error) {
    console.error('Erreur lors de l\'enregistrement du fournisseur:', error);
    showError('Erreur lors de l\'enregistrement du fournisseur: ' + error.message);
  }
}

// Fonction pour ajouter les gestionnaires d'événements aux boutons d'action
function addSupplierActionHandlers() {
  // Gestionnaires pour les boutons d'édition
  document.querySelectorAll('.edit-supplier').forEach(button => {
    button.addEventListener('click', async (e) => {
      const supplierId = e.currentTarget.getAttribute('data-id');
      try {
        const supplier = await supplierManager.getById(supplierId);

        // Remplir le formulaire avec les données du fournisseur
        document.getElementById('supplierId').value = supplier.id;
        document.getElementById('supplierName').value = supplier.name;
        document.getElementById('supplierEmail').value = supplier.email;
        document.getElementById('supplierPhone').value = supplier.phone || '';
        document.getElementById('supplierAddress').value = supplier.address || '';

        // Changer le titre du modal
        document.getElementById('addSupplierModalLabel').textContent = 'Modifier un fournisseur';

        // Ouvrir le modal
        const modal = new bootstrap.Modal(document.getElementById('addSupplierModal'));
        modal.show();
      } catch (error) {
        console.error('Erreur lors de la récupération du fournisseur:', error);
        showError('Erreur lors de la récupération du fournisseur: ' + error.message);
      }
    });
  });

  // Gestionnaires pour les boutons de suppression
  document.querySelectorAll('.delete-supplier').forEach(button => {
    button.addEventListener('click', async (e) => {
      const supplierId = e.currentTarget.getAttribute('data-id');
      if (confirm('Êtes-vous sûr de vouloir supprimer ce fournisseur ?')) {
        try {
          await supplierManager.delete(supplierId);
          loadSuppliersList();
          showSuccess('Fournisseur supprimé avec succès');
        } catch (error) {
          console.error('Erreur lors de la suppression du fournisseur:', error);
          showError('Erreur lors de la suppression du fournisseur: ' + error.message);
        }
      }
    });
  });
}

