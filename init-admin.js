// Script pour initialiser un utilisateur admin par défaut
const db = require('./config/mysql');

async function initializeAdmin() {
  try {
    console.log('🔧 Initialisation de l\'utilisateur admin...');

    // Vérifier si l'utilisateur admin existe déjà
    const existingAdmin = await db.queryOne('SELECT id FROM users WHERE username = ?', ['admin']);
    
    if (existingAdmin) {
      console.log('✅ L\'utilisateur admin existe déjà (ID: ' + existingAdmin.id + ')');
      
      // Mettre à jour le mot de passe pour s'assurer qu'il est "admin"
      await db.query('UPDATE users SET password_hash = ? WHERE username = ?', ['admin', 'admin']);
      console.log('🔑 Mot de passe admin mis à jour');
      
      return;
    }

    // Créer l'utilisateur admin
    const result = await db.query(`
      INSERT INTO users (username, email, password_hash, role, full_name, phone, is_active)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, ['admin', '<EMAIL>', 'admin', 'admin', 'Administrateur', '', 1]);

    console.log('✅ Utilisateur admin créé avec l\'ID:', result.insertId);
    console.log('📋 Identifiants de connexion:');
    console.log('   - Nom d\'utilisateur: admin');
    console.log('   - Mot de passe: admin');

  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation de l\'admin:', error);
    
    // Si la table n'existe pas, la créer
    if (error.code === 'ER_NO_SUCH_TABLE') {
      console.log('🔧 Création de la table users...');
      await createUsersTable();
      // Réessayer
      await initializeAdmin();
    }
  }
}

async function createUsersTable() {
  try {
    await db.query(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role ENUM('viewer', 'cashier', 'manager', 'admin', 'superadmin') DEFAULT 'viewer',
        full_name VARCHAR(100),
        phone VARCHAR(20),
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Table users créée');
  } catch (error) {
    console.error('❌ Erreur lors de la création de la table users:', error);
  }
}

// Fonction pour créer d'autres utilisateurs de test
async function createTestUsers() {
  try {
    console.log('👥 Création d\'utilisateurs de test...');

    const testUsers = [
      {
        username: 'manager',
        email: '<EMAIL>',
        password: 'manager123',
        role: 'manager',
        fullName: 'Gestionnaire',
        phone: '0123456789'
      },
      {
        username: 'cashier',
        email: '<EMAIL>',
        password: 'cashier123',
        role: 'cashier',
        fullName: 'Caissier',
        phone: '0123456790'
      }
    ];

    for (const user of testUsers) {
      // Vérifier si l'utilisateur existe déjà
      const existing = await db.queryOne('SELECT id FROM users WHERE username = ?', [user.username]);
      
      if (!existing) {
        await db.query(`
          INSERT INTO users (username, email, password_hash, role, full_name, phone, is_active)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [user.username, user.email, user.password, user.role, user.fullName, user.phone, 1]);
        
        console.log(`✅ Utilisateur ${user.username} créé`);
      } else {
        console.log(`ℹ️ Utilisateur ${user.username} existe déjà`);
      }
    }

  } catch (error) {
    console.error('❌ Erreur lors de la création des utilisateurs de test:', error);
  }
}

// Exécuter l'initialisation
async function main() {
  try {
    console.log('🚀 Démarrage de l\'initialisation...');
    
    // Tester la connexion à la base de données
    await db.testConnection();
    
    // Initialiser l'admin
    await initializeAdmin();
    
    // Créer des utilisateurs de test
    await createTestUsers();
    
    console.log('🎉 Initialisation terminée avec succès !');
    console.log('');
    console.log('📋 Comptes disponibles:');
    console.log('   - admin / admin (Administrateur)');
    console.log('   - manager / manager123 (Gestionnaire)');
    console.log('   - cashier / cashier123 (Caissier)');
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error);
  } finally {
    process.exit(0);
  }
}

// Exécuter si ce script est appelé directement
if (require.main === module) {
  main();
}

module.exports = { initializeAdmin, createTestUsers, createUsersTable };
