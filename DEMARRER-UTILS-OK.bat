@echo off
title GestiCom - Utils.js Corrige
color 0A

echo.
echo ==========================================
echo   GESTICOM - UTILS.JS CORRIGE
echo ==========================================
echo.

echo PROBLEME RESOLU:
echo ❌ Avant: "productManager is not defined"
echo ✅ Apres: utils.js charge avant products.js
echo.

echo CORRECTIONS APPLIQUEES:
echo ✅ utils.js ajoute dans index.html
echo ✅ Ordre de chargement corrige
echo ✅ Fallback ajoute dans products.js
echo ✅ Verification du chargement
echo.

echo Test de la connexion MySQL...
node quick-test.js

echo.
echo Demarrage du serveur corrige...
echo.

REM Arreter tous les processus Node.js existants
taskkill /F /IM node.exe >nul 2>&1

REM Attendre 2 secondes
ping 127.0.0.1 -n 3 > nul

REM Demarrer le serveur
start "GestiCom Utils Server" /MIN node simple-server.js

REM Attendre 4 secondes
ping 127.0.0.1 -n 5 > nul

echo Ouverture de l'application...
start http://localhost:3000/login.html

echo.
echo ==========================================
echo   GESTICOM - PRODUCTMANAGER OK !
echo ==========================================
echo.
echo Application: http://localhost:3000/login.html
echo Test Utils: http://localhost:3000/test-utils-loading.html
echo Test Actions: http://localhost:3000/test-actions.html
echo.
echo Base de donnees: gesticom (MySQL)
echo.
echo Comptes de connexion:
echo.
echo   admin / admin    (Administrateur)
echo   demo / demo      (Demonstration)
echo.
echo ORDRE DE CHARGEMENT CORRIGE:
echo 1. utils.js (productManager, notifications)
echo 2. products.js (fonctions produits)
echo 3. Autres modules
echo.
echo MAINTENANT FONCTIONNEL:
echo ✅ Page produits se charge sans erreur
echo ✅ Actions (ajouter, modifier, supprimer)
echo ✅ Notifications de succes/erreur
echo ✅ Sauvegarde en base MySQL
echo.
echo PAGES DE TEST:
echo - http://localhost:3000/test-utils-loading.html
echo - http://localhost:3000/test-actions.html
echo.
echo Pour arreter l'application:
echo - Fermez la fenetre "GestiCom Utils Server"
echo - Ou fermez cette fenetre
echo.

pause
