// Test rapide de la connexion MySQL
const mysql = require('mysql2/promise');

async function quickTest() {
  try {
    console.log('🔍 Test rapide de connexion...');
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'gesticom',
      port: 3306
    });
    
    console.log('✅ Connexion réussie à gesticom');
    
    // Test simple
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`📋 ${tables.length} tables trouvées`);
    
    // Test produits
    try {
      const [products] = await connection.execute('SELECT COUNT(*) as count FROM products');
      console.log(`📦 ${products[0].count} produits dans la base`);
    } catch (e) {
      console.log('⚠️ Table products non trouvée');
    }
    
    await connection.end();
    console.log('🎉 Test terminé avec succès');
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

quickTest();
