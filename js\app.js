document.addEventListener('DOMContentLoaded', function() {
  // Vérifier l'authentification de manière plus robuste
  console.log('🔐 Vérification de l\'authentification...');
  console.log('URL actuelle:', window.location.href);
  console.log('Pathname:', window.location.pathname);

  const token = localStorage.getItem('authToken');
  const isLoginPage = window.location.href.includes('login.html') || window.location.pathname.includes('login.html');

  console.log('Token présent:', !!token);
  console.log('Page de connexion:', isLoginPage);

  if (!token && !isLoginPage) {
    console.log('❌ Pas de token et pas sur la page de connexion - Redirection...');
    window.location.href = 'login.html';
    return;
  }

  if (token && isLoginPage) {
    console.log('✅ Token présent sur la page de connexion - Redirection vers l\'app...');
    window.location.href = 'index.html';
    return;
  }

  console.log('✅ Authentification OK, chargement de l\'application...');

  // Gestionnaire de navigation
  const navLinks = document.querySelectorAll('[data-section]');
  navLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const section = this.getAttribute('data-section');
      loadSection(section);
    });
  });

  // Gestionnaire de déconnexion
  document.getElementById('logout').addEventListener('click', function(e) {
    e.preventDefault();
    auth.logout();
  });

  // Gestionnaire du bouton de thème dans la sidebar avec retry
  function setupSidebarThemeButton() {
    const sidebarThemeToggle = document.getElementById('sidebarThemeToggle');
    if (sidebarThemeToggle) {
      // Supprimer les anciens event listeners
      sidebarThemeToggle.replaceWith(sidebarThemeToggle.cloneNode(true));
      const newButton = document.getElementById('sidebarThemeToggle');

      newButton.addEventListener('click', function(e) {
        e.preventDefault();
        if (window.themeManager) {
          window.themeManager.toggleTheme();
          // Forcer la mise à jour après un délai
          setTimeout(() => {
            updateSidebarThemeButton();
            if (window.forceWhiteTextInDarkMode) {
              window.forceWhiteTextInDarkMode();
            }
          }, 100);
        }
      });

      // Mettre à jour le bouton au chargement
      setTimeout(updateSidebarThemeButton, 200);
      console.log('🎨 Bouton de thème sidebar configuré');
    } else {
      // Réessayer si le bouton n'existe pas encore
      setTimeout(setupSidebarThemeButton, 100);
    }
  }

  // Initialiser le bouton de thème
  setupSidebarThemeButton();

  // Gestionnaire du bouton de correction des couleurs
  const fixColorsBtn = document.getElementById('fixColorsBtn');
  if (fixColorsBtn) {
    fixColorsBtn.addEventListener('click', function(e) {
      e.preventDefault();
      console.log('🎨 Correction manuelle des couleurs...');

      // Exécuter toutes les corrections
      if (window.forceWhiteTextInDarkMode) {
        window.forceWhiteTextInDarkMode();
      }

      if (window.forceColoredCardsText) {
        window.forceColoredCardsText();
      }

      if (window.fixAllThemeIssues) {
        window.fixAllThemeIssues();
      }

      // Animation du bouton
      const originalHTML = fixColorsBtn.innerHTML;
      fixColorsBtn.innerHTML = '<i class="bi bi-check"></i>';
      fixColorsBtn.classList.add('btn-success');
      fixColorsBtn.classList.remove('btn-outline-warning');

      setTimeout(() => {
        fixColorsBtn.innerHTML = originalHTML;
        fixColorsBtn.classList.remove('btn-success');
        fixColorsBtn.classList.add('btn-outline-warning');
      }, 1500);
    });
  }

  // Charger la section par défaut
  loadSection('dashboard');

  // Fonction pour charger une section
  function loadSection(section) {
    const sectionTitle = document.getElementById('section-title');
    const container = document.getElementById('app-container');

    // Mettre à jour le titre
    if (sectionTitle) {
      sectionTitle.textContent = getSectionTitle(section);
    }

    // Charger le contenu
    if (container) {
      container.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';

      fetch(`sections/${section}.html`)
        .then(response => {
          if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
          }
          return response.text();
        })
        .then(html => {
          container.innerHTML = html;

          // Appliquer le thème aux nouveaux éléments
          if (window.applyThemeToNewElements) {
            window.applyThemeToNewElements(container);
          }

          // Forcer l'application du thème immédiatement et avec plusieurs tentatives
          applyThemeToContainer(container);

          initSection(section);
        })
        .catch(error => {
          container.innerHTML = '<div class="alert alert-danger">Erreur de chargement du contenu: ' + error.message + '</div>';
          console.error('Erreur:', error);
        });
    } else {
      console.error('Élément container non trouvé');
    }
  }

  // Fonction pour initialiser une section spécifique
  function initSection(section) {
    try {
      switch(section) {
        case 'dashboard':
          initDashboardSection();
          break;
        case 'products':
          initProductsSection();
          break;
        case 'clients':
          initClientsSection();
          break;
        case 'suppliers':
          initSuppliersSection();
          break;
        case 'pos':
          initPOSSection();
          break;
        case 'quotes':
          initQuotesSection();
          break;
        case 'purchase-orders':
          initPurchaseOrdersSection();
          break;
        case 'invoices':
          initInvoicesSection();
          break;
        case 'delivery':
          initDeliverySection();
          break;
        case 'inventory':
          initInventorySection();
          break;
        case 'sales-returns':
          initSalesReturnsSection();
          break;
        case 'purchase-returns':
          initPurchaseReturnsSection();
          break;
        case 'settings':
          initSettingsSection();
          break;
        case 'super-admin':
          initSuperAdminSection();
          break;
        default:
          console.log('Section non reconnue:', section);
      }
    } catch (error) {
      console.error(`Erreur lors de l'initialisation de la section ${section}:`, error);
      document.getElementById('app-container').innerHTML =
        `<div class="alert alert-danger">Erreur lors de l'initialisation de la section: ${error.message}</div>`;
    }
  }

  // Fonction pour obtenir le titre d'une section
  function getSectionTitle(section) {
    const titles = {
      'dashboard': 'Tableau de bord',
      'products': 'Produits',
      'clients': 'Clients',
      'suppliers': 'Fournisseurs',
      'pos': 'Caisse',
      'quotes': 'Devis',
      'purchase-orders': 'Bons de commande',
      'invoices': 'Factures',
      'delivery': 'Bons de livraison',
      'inventory': 'Inventaire',
      'sales-returns': 'Retours clients',
      'purchase-returns': 'Retours fournisseurs',
      'settings': 'Paramètres',
      'super-admin': 'Super Administration'
    };

    return titles[section] || 'Section inconnue';
  }

  // Ajouter cette fonction pour le tableau de bord
  function initDashboardSection() {
    // Initialiser le tableau de bord
    loadDashboardData();

    // Appliquer le thème après un délai pour s'assurer que les données sont chargées
    setTimeout(() => {
      applyThemeAfterDataLoad();
    }, 200);
  }

  // Fonction pour charger les données du tableau de bord
  async function loadDashboardData() {
    try {
      // Récupérer les produits pour les statistiques de stock
      const products = await productManager.getAll();

      // Calculer les statistiques
      const totalProducts = products.length;
      const totalStock = products.reduce((sum, product) => sum + product.stockQuantity, 0);
      const lowStockProducts = products.filter(product => product.stockQuantity < 10).length;
      const outOfStockProducts = products.filter(product => product.stockQuantity === 0).length;

      // Calculer la valeur totale du stock
      const stockValue = products.reduce((sum, product) => sum + (product.price * product.stockQuantity), 0);

      // Mettre à jour les statistiques dans le DOM
      document.getElementById('total-products').textContent = totalProducts;
      document.getElementById('total-stock').textContent = totalStock;
      document.getElementById('low-stock').textContent = lowStockProducts;
      document.getElementById('out-of-stock').textContent = outOfStockProducts;
      document.getElementById('stock-value').textContent = stockValue.toFixed(2) + ' DA';

      // Générer la liste des produits en rupture de stock
      const lowStockList = document.getElementById('low-stock-list');
      lowStockList.innerHTML = '';

      const lowStockItems = products.filter(product => product.stockQuantity < 10);
      if (lowStockItems.length === 0) {
        lowStockList.innerHTML = '<li class="list-group-item">Aucun produit en stock faible</li>';
      } else {
        lowStockItems.forEach(product => {
          lowStockList.innerHTML += `
            <li class="list-group-item d-flex justify-content-between align-items-center">
              ${product.name}
              <span class="badge bg-warning rounded-pill">${product.stockQuantity}</span>
            </li>
          `;
        });
      }

      // Appliquer le thème après le chargement des données
      setTimeout(() => {
        applyThemeAfterDataLoad();
      }, 100);

    } catch (error) {
      console.error('Erreur lors du chargement des données du tableau de bord:', error);
      document.getElementById('dashboard-container').innerHTML =
        '<div class="alert alert-danger">Erreur lors du chargement des données du tableau de bord</div>';
    }
  }

  // Fonction pour appliquer le thème après le chargement des données
  function applyThemeAfterDataLoad() {
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

    if (isDarkMode) {
      console.log('🎨 Application du thème sombre après chargement des données du tableau de bord');

      // Forcer le thème sur les éléments spécifiques du tableau de bord
      const stockValue = document.getElementById('stock-value');
      if (stockValue) {
        stockValue.style.setProperty('color', '#ffffff', 'important');
      }

      const lowStockList = document.getElementById('low-stock-list');
      if (lowStockList) {
        lowStockList.style.setProperty('color', '#ffffff', 'important');

        // Forcer le thème sur tous les éléments de la liste
        const listItems = lowStockList.querySelectorAll('li');
        listItems.forEach(item => {
          item.style.setProperty('background-color', '#495057', 'important');
          item.style.setProperty('color', '#ffffff', 'important');
          item.style.setProperty('border-color', '#6c757d', 'important');
        });
      }

      // Forcer le thème sur tous les éléments du tableau de bord
      const dashboardContainer = document.getElementById('dashboard-container');
      if (dashboardContainer && window.forceThemeOnContainer) {
        window.forceThemeOnContainer(dashboardContainer);
      }

      // Appliquer la fonction de correction rapide
      if (window.quickDarkModeFix) {
        window.quickDarkModeFix();
      }
    }
  }

  // Fonction améliorée pour mettre à jour le bouton de thème dans la sidebar
  function updateSidebarThemeButton() {
    const button = document.getElementById('sidebarThemeToggle');
    if (!button) {
      console.warn('🎨 Bouton sidebar non trouvé pour mise à jour');
      return;
    }

    if (!window.themeManager) {
      console.warn('🎨 ThemeManager non disponible');
      return;
    }

    const currentTheme = window.themeManager.getCurrentTheme();
    console.log(`🎨 Mise à jour bouton sidebar pour thème: ${currentTheme}`);

    if (currentTheme === 'dark') {
      button.innerHTML = '<i class="bi bi-sun-fill me-2"></i>Mode clair';
      button.title = 'Passer au mode clair';
      button.setAttribute('aria-label', 'Passer au mode clair');
    } else {
      button.innerHTML = '<i class="bi bi-moon-fill me-2"></i>Mode sombre';
      button.title = 'Passer au mode sombre';
      button.setAttribute('aria-label', 'Passer au mode sombre');
    }

    // Forcer la couleur du bouton en mode sombre
    if (currentTheme === 'dark') {
      button.style.setProperty('color', '#b0b0b0', 'important');
    } else {
      button.style.removeProperty('color');
    }
  }

  // Rendre la fonction globale pour qu'elle soit accessible
  window.updateSidebarThemeButton = updateSidebarThemeButton;

  // Appliquer le thème à un container spécifique
  function applyThemeToContainer(container) {
    if (!container) return;

    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

    if (isDarkMode) {
      // Application immédiate
      forceThemeOnContainer(container);

      // Applications retardées pour s'assurer que tout est pris en compte
      setTimeout(() => forceThemeOnContainer(container), 50);
      setTimeout(() => forceThemeOnContainer(container), 150);
      setTimeout(() => forceThemeOnContainer(container), 300);

      // Observer les changements dans le container
      observeContainerChanges(container);
    }
  }

  // Forcer le thème sur un container
  function forceThemeOnContainer(container) {
    if (!container) return;

    // Appliquer les fonctions globales de thème
    if (window.forceWhiteTextInDarkMode) {
      window.forceWhiteTextInDarkMode();
    }
    if (window.applyThemeToNewElements) {
      window.applyThemeToNewElements(container);
    }

    // Forcer spécifiquement les éléments du container
    const allElements = container.querySelectorAll('*:not(.btn):not(.badge):not(.alert):not(.card)');
    allElements.forEach(element => {
      const computedStyle = window.getComputedStyle(element);
      const currentColor = computedStyle.color;

      // Vérifier si l'élément a un texte noir ou sombre
      if (currentColor === 'rgb(0, 0, 0)' ||
          currentColor === '#000000' ||
          currentColor === 'rgb(33, 37, 41)' ||
          currentColor === 'rgb(52, 58, 64)' ||
          currentColor === 'rgb(73, 80, 87)') {
        element.style.setProperty('color', '#ffffff', 'important');
      }
    });

    // Forcer les tableaux
    const tables = container.querySelectorAll('table');
    tables.forEach(table => {
      table.style.setProperty('color', '#ffffff', 'important');
      const cells = table.querySelectorAll('td, th');
      cells.forEach(cell => {
        cell.style.setProperty('color', '#ffffff', 'important');
      });
    });

    // Forcer les inputs et selects
    const inputs = container.querySelectorAll('input:not([type="submit"]):not([type="button"]), select, textarea');
    inputs.forEach(input => {
      input.style.setProperty('background-color', '#2d3748', 'important');
      input.style.setProperty('color', '#ffffff', 'important');
      input.style.setProperty('border-color', '#4a5568', 'important');
    });
  }

  // Observer les changements dans le container pour appliquer le thème aux nouveaux éléments
  function observeContainerChanges(container) {
    if (container._themeObserver) return; // Éviter les doublons

    const observer = new MutationObserver((mutations) => {
      let hasNewElements = false;
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          hasNewElements = true;
        }
      });

      if (hasNewElements) {
        setTimeout(() => forceThemeOnContainer(container), 10);
      }
    });

    observer.observe(container, {
      childList: true,
      subtree: true
    });

    container._themeObserver = observer;
  }

  // Rendre les fonctions globales
  window.applyThemeToContainer = applyThemeToContainer;
  window.forceThemeOnContainer = forceThemeOnContainer;
  window.applyThemeAfterDataLoad = applyThemeAfterDataLoad;

  // Écouter les changements de thème pour mettre à jour le tableau de bord
  document.addEventListener('themeChanged', function(event) {
    console.log('🎨 Changement de thème détecté dans app.js:', event.detail.theme);

    // Si on est sur le tableau de bord, appliquer le thème
    const currentSection = window.location.hash.replace('#', '') || 'dashboard';
    if (currentSection === 'dashboard') {
      setTimeout(() => {
        applyThemeAfterDataLoad();
      }, 50);
    }
  });
});










