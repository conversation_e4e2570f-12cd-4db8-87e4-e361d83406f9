<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Mode Sombre - GestiCom</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  <!-- Theme CSS -->
  <link href="css/themes.css" rel="stylesheet">
</head>
<body>
  <div class="container mt-4">
    <div class="row">
      <div class="col-12">
        <h1>Test du Mode Sombre</h1>
        <p>Cette page permet de tester le fonctionnement du mode sombre.</p>

        <div class="mb-3">
          <button id="testThemeToggle" class="btn btn-primary">
            <i class="bi bi-moon-fill me-2"></i>Basculer le thème
          </button>
          <button id="forceWhite" class="btn btn-secondary ms-2">
            Forcer texte blanc
          </button>
          <button id="checkTheme" class="btn btn-info ms-2">
            Vérifier thème actuel
          </button>
        </div>

        <div class="mb-3">
          <button id="runDiagnostic" class="btn btn-warning">
            <i class="bi bi-search me-2"></i>Diagnostic complet
          </button>
          <button id="fixIssues" class="btn btn-success ms-2">
            <i class="bi bi-wrench me-2"></i>Corriger automatiquement
          </button>
        </div>

        <div class="card">
          <div class="card-header">
            <h5 class="card-title">Éléments de test</h5>
          </div>
          <div class="card-body">
            <p class="card-text">Ce texte devrait être blanc en mode sombre.</p>
            <div class="d-flex justify-content-between">
              <span>Texte dans un span</span>
              <strong>Texte en gras</strong>
            </div>

            <table class="table mt-3">
              <thead>
                <tr>
                  <th>Colonne 1</th>
                  <th>Colonne 2</th>
                  <th>Colonne 3</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Données 1</td>
                  <td>Données 2</td>
                  <td>Données 3</td>
                </tr>
              </tbody>
            </table>

            <form>
              <div class="mb-3">
                <label for="testInput" class="form-label">Champ de test</label>
                <input type="text" class="form-control" id="testInput" placeholder="Tapez quelque chose...">
              </div>
              <div class="mb-3">
                <select class="form-select">
                  <option>Option 1</option>
                  <option>Option 2</option>
                </select>
              </div>
            </form>
          </div>
        </div>

        <div class="mt-4">
          <h3>Informations de débogage</h3>
          <div id="debugInfo" class="alert alert-info">
            <p><strong>Thème actuel:</strong> <span id="currentTheme">-</span></p>
            <p><strong>Attribut data-theme:</strong> <span id="dataTheme">-</span></p>
            <p><strong>Couleur du body:</strong> <span id="bodyColor">-</span></p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <!-- Theme JS -->
  <script src="js/theme.js"></script>
  <!-- Theme Diagnostic -->
  <script src="js/theme-diagnostic.js"></script>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const toggleBtn = document.getElementById('testThemeToggle');
      const forceBtn = document.getElementById('forceWhite');
      const checkBtn = document.getElementById('checkTheme');
      const diagnosticBtn = document.getElementById('runDiagnostic');
      const fixBtn = document.getElementById('fixIssues');

      // Bouton de basculement
      toggleBtn.addEventListener('click', function() {
        if (window.themeManager) {
          window.themeManager.toggleTheme();
          updateDebugInfo();
          updateToggleButton();
        }
      });

      // Bouton pour forcer le texte blanc
      forceBtn.addEventListener('click', function() {
        if (window.forceWhiteTextInDarkMode) {
          window.forceWhiteTextInDarkMode();
          updateDebugInfo();
        }
      });

      // Bouton pour vérifier le thème
      checkBtn.addEventListener('click', function() {
        updateDebugInfo();
      });

      // Bouton de diagnostic
      diagnosticBtn.addEventListener('click', function() {
        if (window.runThemeDiagnostic) {
          console.log('🔍 Exécution du diagnostic...');
          window.runThemeDiagnostic();
        }
      });

      // Bouton de correction automatique
      fixBtn.addEventListener('click', function() {
        if (window.fixAllThemeIssues) {
          console.log('🔧 Correction automatique...');
          window.fixAllThemeIssues();
          updateDebugInfo();
        }
      });

      // Fonction pour mettre à jour les informations de débogage
      function updateDebugInfo() {
        const currentTheme = window.themeManager ? window.themeManager.getCurrentTheme() : 'Non disponible';
        const dataTheme = document.documentElement.getAttribute('data-theme') || 'Non défini';
        const bodyColor = window.getComputedStyle(document.body).color;

        document.getElementById('currentTheme').textContent = currentTheme;
        document.getElementById('dataTheme').textContent = dataTheme;
        document.getElementById('bodyColor').textContent = bodyColor;
      }

      // Fonction pour mettre à jour le bouton de basculement
      function updateToggleButton() {
        const currentTheme = window.themeManager ? window.themeManager.getCurrentTheme() : 'light';
        if (currentTheme === 'dark') {
          toggleBtn.innerHTML = '<i class="bi bi-sun-fill me-2"></i>Mode clair';
        } else {
          toggleBtn.innerHTML = '<i class="bi bi-moon-fill me-2"></i>Mode sombre';
        }
      }

      // Initialiser les informations
      setTimeout(() => {
        updateDebugInfo();
        updateToggleButton();
      }, 500);

      // Écouter les changements de thème
      document.addEventListener('themeChanged', function(event) {
        console.log('Changement de thème détecté:', event.detail.theme);
        updateDebugInfo();
        updateToggleButton();
      });
    });
  </script>
</body>
</html>
