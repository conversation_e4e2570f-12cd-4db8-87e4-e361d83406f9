// Script d'initialisation de la base de données GestiCom
const mysql = require('mysql2/promise');

// Configuration de la base de données
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '', // Modifiez selon votre configuration MySQL
  port: 3306,
  charset: 'utf8mb4'
};

console.log('🚀 Initialisation de la base de données GestiCom...\n');

async function setupDatabase() {
  let connection;

  try {
    // Connexion sans spécifier de base de données
    console.log('📡 Connexion à MySQL...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connexion MySQL établie');

    // Créer la base de données si elle n'existe pas
    console.log('🗄️  Création de la base de données...');
    await connection.execute('CREATE DATABASE IF NOT EXISTS gesticom_mysql CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    console.log('✅ Base de données "gesticom_mysql" créée/vérifiée');

    // Utiliser la base de données
    await connection.execute('USE gesticom_mysql');

    // Créer les tables
    console.log('📋 Création des tables...');

    // Table des catégories
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Table "categories" créée');

    // Table des produits
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        stockQuantity INT NOT NULL DEFAULT 0,
        category VARCHAR(100),
        barcode VARCHAR(50) UNIQUE,
        sku VARCHAR(50) UNIQUE,
        minStockLevel INT DEFAULT 0,
        supplier_id INT,
        image_url VARCHAR(500),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_category (category),
        INDEX idx_barcode (barcode),
        INDEX idx_sku (sku),
        INDEX idx_stock (stockQuantity)
      )
    `);
    console.log('✅ Table "products" créée');

    // Table des clients
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS clients (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        contactPerson VARCHAR(255),
        email VARCHAR(255) UNIQUE,
        phone VARCHAR(50),
        address TEXT,
        city VARCHAR(100),
        postalCode VARCHAR(20),
        country VARCHAR(100) DEFAULT 'France',
        tax_number VARCHAR(50),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_name (name)
      )
    `);
    console.log('✅ Table "clients" créée');

    // Table des fournisseurs
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS suppliers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        contactPerson VARCHAR(255),
        email VARCHAR(255) UNIQUE,
        phone VARCHAR(50),
        address TEXT,
        city VARCHAR(100),
        postalCode VARCHAR(20),
        country VARCHAR(100) DEFAULT 'France',
        tax_number VARCHAR(50),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_name (name)
      )
    `);
    console.log('✅ Table "suppliers" créée');

    // Table des factures
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS invoices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        invoiceNumber VARCHAR(50) NOT NULL UNIQUE,
        date DATE NOT NULL,
        clientId INT NOT NULL,
        clientName VARCHAR(255) NOT NULL,
        total DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        tax_amount DECIMAL(10,2) DEFAULT 0.00,
        status ENUM('draft', 'sent', 'paid', 'unpaid', 'cancelled') DEFAULT 'draft',
        due_date DATE,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (clientId) REFERENCES clients(id) ON DELETE CASCADE,
        INDEX idx_invoice_number (invoiceNumber),
        INDEX idx_client (clientId),
        INDEX idx_status (status),
        INDEX idx_date (date)
      )
    `);
    console.log('✅ Table "invoices" créée');

    // Table des utilisateurs
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE,
        full_name VARCHAR(255),
        role ENUM('admin', 'manager', 'cashier', 'user') DEFAULT 'user',
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_role (role)
      )
    `);
    console.log('✅ Table "users" créée');

    // Insérer des données de démonstration
    console.log('📊 Insertion des données de démonstration...');

    // Insérer les catégories
    await connection.execute(`
      INSERT IGNORE INTO categories (name, description) VALUES
      ('Informatique', 'Ordinateurs, composants et accessoires informatiques'),
      ('Téléphonie', 'Smartphones, tablettes et accessoires'),
      ('Bureautique', 'Matériel de bureau et impression'),
      ('Accessoires', 'Accessoires divers'),
      ('Stockage', 'Dispositifs de stockage')
    `);

    // Insérer les produits
    await connection.execute(`
      INSERT IGNORE INTO products (name, description, price, stockQuantity, category, barcode, sku, minStockLevel) VALUES
      ('Ordinateur portable Dell', 'PC portable haute performance 16GB RAM', 89999.99, 15, 'Informatique', '123456789', 'ORD-001', 5),
      ('iPhone 15 Pro', 'Smartphone Apple dernière génération', 149999.99, 8, 'Téléphonie', '987654321', 'TEL-001', 10),
      ('Écran Samsung 27 pouces', 'Moniteur LED 4K 27 pouces', 35999.99, 12, 'Informatique', '456789123', 'ECR-001', 3),
      ('Souris Logitech MX Master', 'Souris sans fil ergonomique', 8999.99, 25, 'Accessoires', '789123456', 'SOU-001', 15),
      ('Clavier mécanique', 'Clavier gaming RGB', 12999.99, 18, 'Accessoires', '321654987', 'CLA-001', 8),
      ('Imprimante HP LaserJet', 'Imprimante laser noir et blanc', 25999.99, 6, 'Bureautique', '654321789', 'IMP-001', 5),
      ('Webcam Logitech 4K', 'Caméra web haute définition', 15999.99, 9, 'Accessoires', '147258369', 'WEB-001', 5),
      ('Disque dur externe 2TB', 'Stockage externe portable', 8999.99, 22, 'Stockage', '963852741', 'HDD-001', 10)
    `);

    // Insérer les clients
    await connection.execute(`
      INSERT IGNORE INTO clients (name, contactPerson, email, phone, address, city, postalCode, country) VALUES
      ('Dupont Entreprise', 'Jean Dupont', '<EMAIL>', '01 23 45 67 89', '123 Rue de Paris', 'Paris', '75001', 'France'),
      ('Martin SARL', 'Marie Martin', '<EMAIL>', '01 98 76 54 32', '45 Avenue des Champs', 'Paris', '75008', 'France'),
      ('Petit Commerce', 'Pierre Petit', '<EMAIL>', '01 45 67 89 10', '78 Boulevard Haussmann', 'Paris', '75009', 'France'),
      ('TechCorp Solutions', 'Sophie Dubois', '<EMAIL>', '01 55 66 77 88', '15 Rue de la Tech', 'Lyon', '69001', 'France')
    `);

    // Insérer les fournisseurs
    await connection.execute(`
      INSERT IGNORE INTO suppliers (name, contactPerson, email, phone, address, city, postalCode, country) VALUES
      ('Tech Supplies Inc.', 'John Smith', '<EMAIL>', '01 23 45 67 89', '123 Tech Street', 'Paris', '75001', 'France'),
      ('Global Electronics', 'Anna Johnson', '<EMAIL>', '01 98 76 54 32', '45 Electronics Avenue', 'Paris', '75008', 'France'),
      ('Office Solutions', 'Mike Wilson', '<EMAIL>', '01 45 67 89 10', '78 Office Boulevard', 'Paris', '75009', 'France')
    `);

    // Insérer les factures
    await connection.execute(`
      INSERT IGNORE INTO invoices (invoiceNumber, date, clientId, clientName, total, status) VALUES
      ('FACT-001', '2024-01-15', 1, 'Dupont Entreprise', 150000.00, 'paid'),
      ('FACT-002', '2024-01-16', 2, 'Martin SARL', 75000.00, 'unpaid'),
      ('FACT-003', '2024-01-17', 4, 'TechCorp Solutions', 220000.00, 'paid')
    `);

    // Insérer les utilisateurs (avec mots de passe hashés)
    try {
      const bcrypt = require('bcrypt');
      const adminPassword = await bcrypt.hash('admin', 10);
      const demoPassword = await bcrypt.hash('demo', 10);

      await connection.execute(
        "INSERT IGNORE INTO users (username, password, email, full_name, role) VALUES (?, ?, ?, ?, ?)",
        ['admin', adminPassword, '<EMAIL>', 'Administrateur', 'admin']
      );

      await connection.execute(
        "INSERT IGNORE INTO users (username, password, email, full_name, role) VALUES (?, ?, ?, ?, ?)",
        ['demo', demoPassword, '<EMAIL>', 'Utilisateur Démo', 'user']
      );
    } catch (userError) {
      console.log('⚠️ Erreur lors de l\'insertion des utilisateurs (bcrypt peut ne pas être disponible)');
      // Insérer des utilisateurs avec mots de passe simples pour les tests
      await connection.execute(`
        INSERT IGNORE INTO users (username, password, email, full_name, role) VALUES
        ('admin', 'admin', '<EMAIL>', 'Administrateur', 'admin'),
        ('demo', 'demo', '<EMAIL>', 'Utilisateur Démo', 'user')
      `);
    }

    console.log('✅ Données de démonstration insérées');

    console.log('\n🎉 Base de données GestiCom initialisée avec succès !');
    console.log('\n📊 Résumé :');
    console.log('• Base de données : gesticom');
    console.log('• Tables créées : 6 (products, clients, suppliers, invoices, users, categories)');
    console.log('• Données de démo : Oui');
    console.log('• Utilisateurs : admin/admin, demo/demo');
    console.log('\n🚀 Vous pouvez maintenant démarrer l\'application !');

  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation :', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Exécuter l'initialisation
setupDatabase();
