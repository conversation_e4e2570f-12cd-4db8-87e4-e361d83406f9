// ===== GESTIONNAIRE D'API POUR GESTICOM =====

class ApiManager {
  constructor() {
    this.baseUrl = '';
    this.defaultHeaders = {
      'Content-Type': 'application/json'
    };
  }

  // ===== MÉTHODES UTILITAIRES =====
  
  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const config = {
      headers: { ...this.defaultHeaders, ...options.headers },
      ...options
    };

    try {
      console.log(`🌐 API Request: ${config.method || 'GET'} ${url}`);
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status} - ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`✅ API Response: ${endpoint}`, data);
      return data;
    } catch (error) {
      console.error(`❌ API Error: ${endpoint}`, error);
      throw error;
    }
  }

  async get(endpoint) {
    return this.request(endpoint, { method: 'GET' });
  }

  async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  async put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }

  // ===== PRODUITS =====
  
  async getProducts() {
    return this.get('/api/products');
  }

  async getProduct(id) {
    return this.get(`/api/products/${id}`);
  }

  async createProduct(productData) {
    return this.post('/api/products', productData);
  }

  async updateProduct(id, productData) {
    return this.put(`/api/products/${id}`, productData);
  }

  async deleteProduct(id) {
    return this.delete(`/api/products/${id}`);
  }

  // ===== CLIENTS =====
  
  async getClients() {
    return this.get('/api/clients');
  }

  async getClient(id) {
    return this.get(`/api/clients/${id}`);
  }

  async createClient(clientData) {
    return this.post('/api/clients', clientData);
  }

  async updateClient(id, clientData) {
    return this.put(`/api/clients/${id}`, clientData);
  }

  async deleteClient(id) {
    return this.delete(`/api/clients/${id}`);
  }

  // ===== FOURNISSEURS =====
  
  async getSuppliers() {
    return this.get('/api/suppliers');
  }

  async getSupplier(id) {
    return this.get(`/api/suppliers/${id}`);
  }

  async createSupplier(supplierData) {
    return this.post('/api/suppliers', supplierData);
  }

  async updateSupplier(id, supplierData) {
    return this.put(`/api/suppliers/${id}`, supplierData);
  }

  async deleteSupplier(id) {
    return this.delete(`/api/suppliers/${id}`);
  }

  // ===== FACTURES =====
  
  async getInvoices() {
    return this.get('/api/invoices');
  }

  async getInvoice(id) {
    return this.get(`/api/invoices/${id}`);
  }

  async createInvoice(invoiceData) {
    return this.post('/api/invoices', invoiceData);
  }

  async updateInvoice(id, invoiceData) {
    return this.put(`/api/invoices/${id}`, invoiceData);
  }

  async deleteInvoice(id) {
    return this.delete(`/api/invoices/${id}`);
  }

  // ===== CATÉGORIES =====
  
  async getCategories() {
    return this.get('/api/categories');
  }

  // ===== STATISTIQUES =====
  
  async getDashboardStats() {
    try {
      const products = await this.getProducts();
      
      return {
        totalProducts: products.length,
        totalStock: products.reduce((sum, product) => sum + product.stockQuantity, 0),
        lowStockProducts: products.filter(product => product.stockQuantity < 10).length,
        outOfStockProducts: products.filter(product => product.stockQuantity === 0).length,
        stockValue: products.reduce((sum, product) => sum + (product.price * product.stockQuantity), 0),
        lowStockItems: products.filter(product => product.stockQuantity < 10)
      };
    } catch (error) {
      console.error('❌ Erreur lors du calcul des statistiques:', error);
      throw error;
    }
  }

  // ===== AUTHENTIFICATION =====
  
  async login(credentials) {
    return this.post('/api/auth/login', credentials);
  }

  async logout() {
    return this.post('/api/auth/logout');
  }

  async verifyToken(token) {
    return this.post('/api/auth/verify', { token });
  }

  // ===== GESTION DES ERREURS =====
  
  handleError(error, context = '') {
    console.error(`❌ Erreur API ${context}:`, error);
    
    // Afficher une notification à l'utilisateur
    if (window.showNotification) {
      window.showNotification('Erreur de connexion', 'error');
    }
    
    // Rediriger vers la page de connexion si non authentifié
    if (error.message.includes('401') || error.message.includes('Unauthorized')) {
      localStorage.removeItem('authToken');
      window.location.replace('login.html');
    }
    
    throw error;
  }
}

// ===== GESTIONNAIRE GLOBAL =====

// Créer une instance globale
const apiManager = new ApiManager();

// Rendre disponible globalement
window.apiManager = apiManager;

// Compatibilité avec l'ancien code
window.productManager = {
  getAll: () => apiManager.getProducts(),
  get: (id) => apiManager.getProduct(id),
  create: (data) => apiManager.createProduct(data),
  update: (id, data) => apiManager.updateProduct(id, data),
  delete: (id) => apiManager.deleteProduct(id)
};

window.clientManager = {
  getAll: () => apiManager.getClients(),
  get: (id) => apiManager.getClient(id),
  create: (data) => apiManager.createClient(data),
  update: (id, data) => apiManager.updateClient(id, data),
  delete: (id) => apiManager.deleteClient(id)
};

window.supplierManager = {
  getAll: () => apiManager.getSuppliers(),
  get: (id) => apiManager.getSupplier(id),
  create: (data) => apiManager.createSupplier(data),
  update: (id, data) => apiManager.updateSupplier(id, data),
  delete: (id) => apiManager.deleteSupplier(id)
};

window.invoiceManager = {
  getAll: () => apiManager.getInvoices(),
  get: (id) => apiManager.getInvoice(id),
  create: (data) => apiManager.createInvoice(data),
  update: (id, data) => apiManager.updateInvoice(id, data),
  delete: (id) => apiManager.deleteInvoice(id)
};

console.log('🌐 Gestionnaire d\'API initialisé');
