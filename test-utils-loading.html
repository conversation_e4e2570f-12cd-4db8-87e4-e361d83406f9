<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chargement Utils - GestiCom</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test du Chargement des Utilitaires</h1>
        
        <div class="alert alert-info">
            <strong>Test de l'ordre de chargement des scripts</strong>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Vérifications</h3>
                <div id="checks" class="list-group">
                    <div class="list-group-item">
                        <span id="utils-check">⏳</span> utils.js chargé
                    </div>
                    <div class="list-group-item">
                        <span id="productmanager-check">⏳</span> productManager disponible
                    </div>
                    <div class="list-group-item">
                        <span id="notifications-check">⏳</span> Fonctions de notification disponibles
                    </div>
                    <div class="list-group-item">
                        <span id="products-check">⏳</span> products.js chargé
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>Test API</h3>
                <div class="d-grid gap-2">
                    <button id="testAPI" class="btn btn-primary">Tester API Produits</button>
                    <button id="testNotifications" class="btn btn-success">Tester Notifications</button>
                    <button id="testProductManager" class="btn btn-info">Tester ProductManager</button>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Logs</h3>
            <div id="logs" class="border p-3" style="height: 300px; overflow-y: auto; background-color: #f8f9fa;">
                <div class="text-muted">Les logs apparaîtront ici...</div>
            </div>
        </div>
    </div>

    <!-- Scripts dans le bon ordre -->
    <script src="js/utils.js"></script>
    <script src="js/products.js"></script>

    <script>
        const logsDiv = document.getElementById('logs');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-dark';
            logsDiv.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function updateCheck(id, status, message) {
            const element = document.getElementById(id);
            if (status === 'success') {
                element.textContent = '✅';
                element.parentElement.classList.add('list-group-item-success');
            } else if (status === 'error') {
                element.textContent = '❌';
                element.parentElement.classList.add('list-group-item-danger');
            } else {
                element.textContent = '⏳';
            }
            addLog(message, status);
        }
        
        // Vérifications au chargement
        window.addEventListener('load', function() {
            addLog('🚀 Page chargée, début des vérifications...', 'info');
            
            // Vérifier utils.js
            if (typeof productManager !== 'undefined') {
                updateCheck('utils-check', 'success', 'utils.js chargé avec succès');
                updateCheck('productmanager-check', 'success', 'productManager disponible');
            } else {
                updateCheck('utils-check', 'error', 'utils.js non chargé ou productManager non défini');
                updateCheck('productmanager-check', 'error', 'productManager non disponible');
            }
            
            // Vérifier les notifications
            if (typeof showSuccess !== 'undefined' && typeof showError !== 'undefined') {
                updateCheck('notifications-check', 'success', 'Fonctions de notification disponibles');
            } else {
                updateCheck('notifications-check', 'error', 'Fonctions de notification non disponibles');
            }
            
            // Vérifier products.js
            if (typeof loadProductsList !== 'undefined') {
                updateCheck('products-check', 'success', 'products.js chargé avec succès');
            } else {
                updateCheck('products-check', 'error', 'products.js non chargé ou fonctions non définies');
            }
        });
        
        // Test API
        document.getElementById('testAPI').addEventListener('click', async function() {
            addLog('🧪 Test de l\'API produits...', 'info');
            try {
                const response = await fetch('/api/products');
                const data = await response.json();
                addLog(`✅ API OK: ${data.length} produits récupérés`, 'success');
            } catch (error) {
                addLog(`❌ Erreur API: ${error.message}`, 'error');
            }
        });
        
        // Test notifications
        document.getElementById('testNotifications').addEventListener('click', function() {
            addLog('🧪 Test des notifications...', 'info');
            try {
                if (typeof showSuccess !== 'undefined') {
                    showSuccess('Test de notification de succès');
                    addLog('✅ showSuccess() fonctionne', 'success');
                } else {
                    addLog('❌ showSuccess() non disponible', 'error');
                }
                
                setTimeout(() => {
                    if (typeof showError !== 'undefined') {
                        showError('Test de notification d\'erreur');
                        addLog('✅ showError() fonctionne', 'success');
                    } else {
                        addLog('❌ showError() non disponible', 'error');
                    }
                }, 1000);
            } catch (error) {
                addLog(`❌ Erreur notifications: ${error.message}`, 'error');
            }
        });
        
        // Test ProductManager
        document.getElementById('testProductManager').addEventListener('click', async function() {
            addLog('🧪 Test du ProductManager...', 'info');
            try {
                if (typeof productManager !== 'undefined') {
                    const products = await productManager.getAll();
                    addLog(`✅ ProductManager.getAll(): ${products.length} produits`, 'success');
                    
                    if (products.length > 0) {
                        const firstProduct = await productManager.getById(products[0].id);
                        addLog(`✅ ProductManager.getById(): ${firstProduct.name}`, 'success');
                    }
                } else {
                    addLog('❌ ProductManager non disponible', 'error');
                }
            } catch (error) {
                addLog(`❌ Erreur ProductManager: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
