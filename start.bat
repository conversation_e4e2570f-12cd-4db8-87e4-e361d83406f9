@echo off
title GestiCom - Demarrage Automatique
color 0A

echo.
echo ========================================
echo    GESTICOM - DEMARRAGE AUTOMATIQUE
echo ========================================
echo.

echo [1/4] Verification de Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Node.js n'est pas installe ou non trouve dans le PATH
    echo Veuillez installer Node.js depuis https://nodejs.org/
    pause
    exit /b 1
)
echo ✓ Node.js detecte

echo.
echo [2/4] Verification des dependances...
if not exist "node_modules" (
    echo Installation des dependances...
    npm install
    if %errorlevel% neq 0 (
        echo ERREUR: Echec de l'installation des dependances
        pause
        exit /b 1
    )
)
echo ✓ Dependances OK

echo.
echo [3/4] Demarrage du serveur GestiCom...
echo Serveur en cours de demarrage sur http://localhost:3000
echo.

REM Demarrer le serveur en arriere-plan
start /B node simple-server.js

REM Attendre que le serveur soit pret
echo Attente du demarrage du serveur...
timeout /t 3 /nobreak >nul

REM Verifier si le serveur repond
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3000/api/status' -TimeoutSec 5 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% neq 0 (
    echo Attente supplementaire...
    timeout /t 2 /nobreak >nul
)

echo ✓ Serveur demarre

echo.
echo [4/4] Ouverture de l'application...
echo Ouverture de GestiCom dans votre navigateur...

REM Ouvrir l'application dans le navigateur par defaut
start http://localhost:3000/login.html

echo.
echo ========================================
echo   GESTICOM EST MAINTENANT ACTIF !
echo ========================================
echo.
echo ✓ Serveur: http://localhost:3000
echo ✓ Application: http://localhost:3000/login.html
echo ✓ API Status: http://localhost:3000/api/status
echo.
echo Comptes de demonstration:
echo • admin / admin (Administrateur)
echo • demo / demo (Demonstration)
echo.
echo Pour arreter le serveur, fermez cette fenetre
echo ou appuyez sur Ctrl+C
echo.

REM Garder la fenetre ouverte et afficher les logs du serveur
echo Logs du serveur:
echo ----------------------------------------
node simple-server.js
