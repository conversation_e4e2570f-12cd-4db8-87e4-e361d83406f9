// Script pour vérifier les données dans la base gesticom
const mysql = require('mysql2/promise');

async function verifyData() {
  try {
    console.log('🔍 Vérification des données dans gesticom...\n');
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'gesticom',
      port: 3306
    });
    
    console.log('✅ Connexion établie à gesticom');

    // Vérifier les produits
    console.log('\n📦 PRODUITS:');
    try {
      const [products] = await connection.execute('SELECT id, name, price, stockQuantity FROM products LIMIT 10');
      console.log(`✅ ${products.length} produits trouvés:`);
      products.forEach(product => {
        console.log(`  - ${product.name} (${product.price}€, stock: ${product.stockQuantity})`);
      });
    } catch (e) {
      console.log('❌ Erreur produits:', e.message);
    }

    // Vérifier les clients
    console.log('\n👥 CLIENTS:');
    try {
      const [clients] = await connection.execute('SELECT id, name, email FROM clients LIMIT 10');
      console.log(`✅ ${clients.length} clients trouvés:`);
      clients.forEach(client => {
        console.log(`  - ${client.name} (${client.email || 'Pas d\'email'})`);
      });
    } catch (e) {
      console.log('❌ Erreur clients:', e.message);
    }

    // Vérifier les fournisseurs
    console.log('\n🏢 FOURNISSEURS:');
    try {
      const [suppliers] = await connection.execute('SELECT id, name, email FROM suppliers LIMIT 10');
      console.log(`✅ ${suppliers.length} fournisseurs trouvés:`);
      suppliers.forEach(supplier => {
        console.log(`  - ${supplier.name} (${supplier.email || 'Pas d\'email'})`);
      });
    } catch (e) {
      console.log('❌ Erreur fournisseurs:', e.message);
    }

    // Vérifier les factures
    console.log('\n🧾 FACTURES:');
    try {
      const [invoices] = await connection.execute('SELECT id, invoiceNumber, total, status FROM invoices LIMIT 10');
      console.log(`✅ ${invoices.length} factures trouvées:`);
      invoices.forEach(invoice => {
        console.log(`  - ${invoice.invoiceNumber} (${invoice.total}€, ${invoice.status})`);
      });
    } catch (e) {
      console.log('❌ Erreur factures:', e.message);
    }

    // Vérifier les utilisateurs
    console.log('\n👤 UTILISATEURS:');
    try {
      const [users] = await connection.execute('SELECT id, username, role FROM users LIMIT 10');
      console.log(`✅ ${users.length} utilisateurs trouvés:`);
      users.forEach(user => {
        console.log(`  - ${user.username} (${user.role})`);
      });
    } catch (e) {
      console.log('❌ Erreur utilisateurs:', e.message);
    }

    await connection.end();
    
    console.log('\n🎉 Vérification terminée !');
    console.log('💡 L\'application GestiCom est maintenant connectée à votre base de données MySQL');
    console.log('🚀 Vous pouvez utiliser l\'application avec vos vraies données !');
    
  } catch (error) {
    console.log('❌ Erreur de connexion:', error.message);
  }
}

verifyData();
