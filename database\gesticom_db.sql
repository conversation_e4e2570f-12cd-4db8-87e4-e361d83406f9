-- Création de la base de données GestiCom
CREATE DATABASE gesticom;

-- Utilisation de la base de données
\c gesticom;

-- Table des utilisateurs
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    role VARCHAR(20) NOT NULL DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- Table des catégories de produits
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des produits
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(12, 2) NOT NULL,
    stock_quantity INTEGER NOT NULL DEFAULT 0,
    category_id INTEGER REFERENCES categories(id),
    barcode VARCHAR(50),
    sku VARCHAR(50),
    min_stock_level INTEGER DEFAULT 5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des clients
CREATE TABLE clients (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    address TEXT,
    city VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'Algérie',
    phone VARCHAR(20),
    email VARCHAR(100),
    tax_id VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des fournisseurs
CREATE TABLE suppliers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    address TEXT,
    city VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'Algérie',
    phone VARCHAR(20),
    email VARCHAR(100),
    tax_id VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des devis
CREATE TABLE quotes (
    id SERIAL PRIMARY KEY,
    quote_number VARCHAR(20) NOT NULL UNIQUE,
    client_id INTEGER NOT NULL REFERENCES clients(id),
    date DATE NOT NULL,
    expiry_date DATE,
    subtotal DECIMAL(12, 2) NOT NULL,
    tax_rate DECIMAL(5, 2) DEFAULT 19.00,
    tax_amount DECIMAL(12, 2) NOT NULL,
    total DECIMAL(12, 2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- pending, accepted, rejected, expired
    notes TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des détails de devis
CREATE TABLE quote_items (
    id SERIAL PRIMARY KEY,
    quote_id INTEGER NOT NULL REFERENCES quotes(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(12, 2) NOT NULL,
    discount_percent DECIMAL(5, 2) DEFAULT 0,
    tax_percent DECIMAL(5, 2) DEFAULT 19.00,
    total DECIMAL(12, 2) NOT NULL
);

-- Table des commandes fournisseurs
CREATE TABLE purchase_orders (
    id SERIAL PRIMARY KEY,
    order_number VARCHAR(20) NOT NULL UNIQUE,
    supplier_id INTEGER NOT NULL REFERENCES suppliers(id),
    date DATE NOT NULL,
    expected_delivery_date DATE,
    subtotal DECIMAL(12, 2) NOT NULL,
    tax_rate DECIMAL(5, 2) DEFAULT 19.00,
    tax_amount DECIMAL(12, 2) NOT NULL,
    total DECIMAL(12, 2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- pending, confirmed, received, cancelled
    notes TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des détails de commandes fournisseurs
CREATE TABLE purchase_order_items (
    id SERIAL PRIMARY KEY,
    purchase_order_id INTEGER NOT NULL REFERENCES purchase_orders(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(12, 2) NOT NULL,
    discount_percent DECIMAL(5, 2) DEFAULT 0,
    tax_percent DECIMAL(5, 2) DEFAULT 19.00,
    total DECIMAL(12, 2) NOT NULL
);

-- Table des factures
CREATE TABLE invoices (
    id SERIAL PRIMARY KEY,
    invoice_number VARCHAR(20) NOT NULL UNIQUE,
    client_id INTEGER NOT NULL REFERENCES clients(id),
    quote_id INTEGER REFERENCES quotes(id),
    date DATE NOT NULL,
    due_date DATE,
    subtotal DECIMAL(12, 2) NOT NULL,
    tax_rate DECIMAL(5, 2) DEFAULT 19.00,
    tax_amount DECIMAL(12, 2) NOT NULL,
    total DECIMAL(12, 2) NOT NULL,
    amount_paid DECIMAL(12, 2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'unpaid', -- unpaid, partial, paid, cancelled
    notes TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des détails de factures
CREATE TABLE invoice_items (
    id SERIAL PRIMARY KEY,
    invoice_id INTEGER NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(12, 2) NOT NULL,
    discount_percent DECIMAL(5, 2) DEFAULT 0,
    tax_percent DECIMAL(5, 2) DEFAULT 19.00,
    total DECIMAL(12, 2) NOT NULL
);

-- Table des paiements
CREATE TABLE payments (
    id SERIAL PRIMARY KEY,
    invoice_id INTEGER NOT NULL REFERENCES invoices(id),
    date DATE NOT NULL,
    amount DECIMAL(12, 2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL, -- cash, check, bank_transfer, credit_card
    reference VARCHAR(100),
    notes TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des bons de livraison
CREATE TABLE delivery_notes (
    id SERIAL PRIMARY KEY,
    delivery_number VARCHAR(20) NOT NULL UNIQUE,
    invoice_id INTEGER REFERENCES invoices(id),
    client_id INTEGER NOT NULL REFERENCES clients(id),
    date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- pending, delivered, cancelled
    notes TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des détails de bons de livraison
CREATE TABLE delivery_note_items (
    id SERIAL PRIMARY KEY,
    delivery_note_id INTEGER NOT NULL REFERENCES delivery_notes(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL REFERENCES products(id),
    quantity INTEGER NOT NULL
);

-- Table des retours clients
CREATE TABLE sales_returns (
    id SERIAL PRIMARY KEY,
    return_number VARCHAR(20) NOT NULL UNIQUE,
    client_id INTEGER NOT NULL REFERENCES clients(id),
    invoice_id INTEGER REFERENCES invoices(id),
    date DATE NOT NULL,
    subtotal DECIMAL(12, 2) NOT NULL,
    tax_rate DECIMAL(5, 2) DEFAULT 19.00,
    tax_amount DECIMAL(12, 2) NOT NULL,
    total DECIMAL(12, 2) NOT NULL,
    reason TEXT,
    status VARCHAR(20) DEFAULT 'pending', -- pending, processed, refunded, cancelled
    notes TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des détails de retours clients
CREATE TABLE sales_return_items (
    id SERIAL PRIMARY KEY,
    sales_return_id INTEGER NOT NULL REFERENCES sales_returns(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(12, 2) NOT NULL,
    total DECIMAL(12, 2) NOT NULL
);

-- Table des retours fournisseurs
CREATE TABLE purchase_returns (
    id SERIAL PRIMARY KEY,
    return_number VARCHAR(20) NOT NULL UNIQUE,
    supplier_id INTEGER NOT NULL REFERENCES suppliers(id),
    purchase_order_id INTEGER REFERENCES purchase_orders(id),
    date DATE NOT NULL,
    subtotal DECIMAL(12, 2) NOT NULL,
    tax_rate DECIMAL(5, 2) DEFAULT 19.00,
    tax_amount DECIMAL(12, 2) NOT NULL,
    total DECIMAL(12, 2) NOT NULL,
    reason TEXT,
    status VARCHAR(20) DEFAULT 'pending', -- pending, processed, credited, cancelled
    notes TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des détails de retours fournisseurs
CREATE TABLE purchase_return_items (
    id SERIAL PRIMARY KEY,
    purchase_return_id INTEGER NOT NULL REFERENCES purchase_returns(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(12, 2) NOT NULL,
    total DECIMAL(12, 2) NOT NULL
);

-- Table des mouvements de stock
CREATE TABLE inventory_movements (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES products(id),
    date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    quantity INTEGER NOT NULL, -- positif pour entrée, négatif pour sortie
    type VARCHAR(20) NOT NULL, -- purchase, sale, return, adjustment
    reference_id INTEGER, -- ID de la facture, commande, etc.
    reference_type VARCHAR(50), -- invoice, purchase_order, etc.
    notes TEXT,
    created_by INTEGER REFERENCES users(id)
);

-- Table des paramètres de l'entreprise
CREATE TABLE company_settings (
    id SERIAL PRIMARY KEY,
    company_name VARCHAR(100) NOT NULL,
    address TEXT,
    city VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'Algérie',
    phone VARCHAR(20),
    email VARCHAR(100),
    website VARCHAR(100),
    tax_id VARCHAR(50),
    article VARCHAR(50),
    register VARCHAR(50),
    logo BYTEA,
    invoice_prefix VARCHAR(10) DEFAULT 'FACT-',
    quote_prefix VARCHAR(10) DEFAULT 'DEV-',
    delivery_prefix VARCHAR(10) DEFAULT 'BL-',
    purchase_order_prefix VARCHAR(10) DEFAULT 'BC-',
    footer_text TEXT,
    terms_and_conditions TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insertion d'un utilisateur administrateur par défaut (mot de passe: admin)
INSERT INTO users (username, password, full_name, email, role) 
VALUES ('admin', '$2a$10$8KzaNdKwt.Yl9Bh8EJEyZOT0gXujWWxkEBHDLsz1jlLOI1G7lQBrC', 'Administrateur', '<EMAIL>', 'admin');

-- Insertion de quelques catégories de base
INSERT INTO categories (name, description) VALUES 
('Informatique', 'Produits informatiques et accessoires'),
('Téléphonie', 'Téléphones mobiles et accessoires'),
('Bureautique', 'Fournitures et équipements de bureau'),
('Accessoires', 'Accessoires divers');

-- Insertion de quelques produits de test
INSERT INTO products (name, description, price, stock_quantity, category_id, min_stock_level) VALUES 
('Ordinateur portable', 'Ordinateur portable 15.6" Core i5', 89999.99, 15, 1, 3),
('Smartphone', 'Smartphone Android 6.5"', 49999.99, 25, 2, 5),
('Écran 24 pouces', 'Écran LED Full HD 24"', 19999.99, 10, 1, 2),
('Souris sans fil', 'Souris optique sans fil', 2999.99, 50, 4, 10);

-- Insertion des paramètres de l'entreprise par défaut
INSERT INTO company_settings (
    company_name, address, city, country, phone, email, 
    invoice_prefix, quote_prefix, delivery_prefix, 
    footer_text, terms_and_conditions
) VALUES (
    'GestiCom', '123 Rue du Commerce', 'Alger', 'Algérie', '021 23 45 67', '<EMAIL>',
    'FACT-', 'DEV-', 'BL-',
    'Merci pour votre confiance', 'Paiement à réception de facture'
);
