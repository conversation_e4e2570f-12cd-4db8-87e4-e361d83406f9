const express = require('express');
const path = require('path');
const cors = require('cors');
const db = require('./config/mysql');
const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('./'));

// Tester la connexion à la base de données au démarrage
console.log('🚀 Démarrage du serveur GestiCom...');
db.testConnection();

// Données de fallback si MySQL n'est pas disponible
const fallbackData = {
  products: [
    { id: 1, name: "Ordinateur portable", description: "Ordinateur portable haute performance", price: 89999.99, stockQuantity: 15, category: "Informatique", barcode: "123456789", sku: "ORD-001", minStockLevel: 5 },
    { id: 2, name: "Smartphone", description: "Smartphone dernière génération", price: 49999.99, stockQuantity: 25, category: "Téléphonie", barcode: "987654321", sku: "TEL-001", minStockLevel: 10 },
    { id: 3, name: "Écran 24 pouces", description: "Écran LED 24 pouces Full HD", price: 19999.99, stockQuantity: 10, category: "Informatique", barcode: "*********", sku: "ECR-001", minStockLevel: 3 },
    { id: 4, name: "Souris sans fil", description: "Souris optique sans fil", price: 2999.99, stockQuantity: 50, category: "Accessoires", barcode: "*********", sku: "SOU-001", minStockLevel: 15 }
  ],
  clients: [
    { id: 1, name: "Dupont Entreprise", contactPerson: "Jean Dupont", email: "<EMAIL>", phone: "01 23 45 67 89", address: "123 Rue de Paris", city: "Paris", postalCode: "75001", country: "France", taxId: "FR123456789", notes: "" },
    { id: 2, name: "Martin SARL", contactPerson: "Marie Martin", email: "<EMAIL>", phone: "01 98 76 54 32", address: "45 Avenue des Champs", city: "Paris", postalCode: "75008", country: "France", taxId: "FR987654321", notes: "" },
    { id: 3, name: "Petit Commerce", contactPerson: "Pierre Petit", email: "<EMAIL>", phone: "01 45 67 89 10", address: "78 Boulevard Haussmann", city: "Paris", postalCode: "75009", country: "France", taxId: "FR*********", notes: "" }
  ]
};

// Routes API avec base de données MySQL et fallback
app.get('/api/products', async (req, res) => {
  try {
    console.log('🔍 Tentative de récupération des produits depuis MySQL...');
    const products = await db.query(`
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      ORDER BY p.id
    `);

    console.log(`✅ ${products.length} produits récupérés depuis MySQL`);

    // Transformer les données pour correspondre au format attendu par le frontend
    const formattedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      price: parseFloat(product.price),
      stockQuantity: product.stock_quantity,
      category: product.category || 'Sans catégorie',
      barcode: product.barcode,
      sku: product.sku,
      minStockLevel: product.min_stock_level
    }));

    res.json(formattedProducts);
  } catch (error) {
    console.error('❌ Erreur MySQL, utilisation des données de fallback:', error.message);
    res.json(fallbackData.products);
  }
});

app.get('/api/clients', async (req, res) => {
  try {
    console.log('🔍 Tentative de récupération des clients depuis MySQL...');
    const clients = await db.query('SELECT * FROM clients ORDER BY id');

    console.log(`✅ ${clients.length} clients récupérés depuis MySQL`);

    // Transformer les données pour correspondre au format attendu par le frontend
    const formattedClients = clients.map(client => ({
      id: client.id,
      name: client.name,
      contactPerson: client.contact_person,
      email: client.email,
      phone: client.phone,
      address: client.address,
      city: client.city,
      postalCode: client.postal_code,
      country: client.country,
      taxId: client.tax_id,
      notes: client.notes
    }));

    res.json(formattedClients);
  } catch (error) {
    console.error('❌ Erreur MySQL, utilisation des données de fallback:', error.message);
    res.json(fallbackData.clients);
  }
});

app.get('/api/suppliers', async (req, res) => {
  try {
    console.log('🔍 Tentative de récupération des fournisseurs depuis MySQL...');
    const suppliers = await db.query('SELECT * FROM suppliers ORDER BY id');

    console.log(`✅ ${suppliers.length} fournisseurs récupérés depuis MySQL`);

    // Transformer les données pour correspondre au format attendu par le frontend
    const formattedSuppliers = suppliers.map(supplier => ({
      id: supplier.id,
      name: supplier.name,
      contactPerson: supplier.contact_person,
      email: supplier.email,
      phone: supplier.phone,
      address: supplier.address,
      city: supplier.city,
      postalCode: supplier.postal_code,
      country: supplier.country,
      taxId: supplier.tax_id,
      notes: supplier.notes
    }));

    res.json(formattedSuppliers);
  } catch (error) {
    console.error('❌ Erreur MySQL, utilisation des données de fallback:', error.message);
    // Fallback avec des données de test
    res.json([
      { id: 1, name: "Tech Supplies Inc.", email: "<EMAIL>", phone: "01 23 45 67 89", address: "123 Tech Street, 75001 Paris" },
      { id: 2, name: "Global Electronics", email: "<EMAIL>", phone: "01 98 76 54 32", address: "45 Electronics Avenue, 75008 Paris" },
      { id: 3, name: "Office Solutions", email: "<EMAIL>", phone: "01 45 67 89 10", address: "78 Office Boulevard, 75009 Paris" }
    ]);
  }
});

// Route pour récupérer un fournisseur spécifique
app.get('/api/suppliers/:id', async (req, res) => {
  try {
    const supplierId = req.params.id;
    console.log(`🔍 Récupération du fournisseur ${supplierId}...`);

    const supplier = await db.queryOne('SELECT * FROM suppliers WHERE id = ?', [supplierId]);

    if (!supplier) {
      return res.status(404).json({ error: 'Fournisseur non trouvé' });
    }

    // Transformer les données pour correspondre au format attendu par le frontend
    const formattedSupplier = {
      id: supplier.id,
      name: supplier.name,
      contactPerson: supplier.contact_person,
      email: supplier.email,
      phone: supplier.phone,
      address: supplier.address,
      city: supplier.city,
      postalCode: supplier.postal_code,
      country: supplier.country,
      taxId: supplier.tax_id,
      notes: supplier.notes
    };

    res.json(formattedSupplier);
  } catch (error) {
    console.error('❌ Erreur lors de la récupération du fournisseur:', error.message);
    // Fallback avec des données de test
    const testSuppliers = [
      { id: 1, name: "Tech Supplies Inc.", email: "<EMAIL>", phone: "01 23 45 67 89", address: "123 Tech Street, 75001 Paris" },
      { id: 2, name: "Global Electronics", email: "<EMAIL>", phone: "01 98 76 54 32", address: "45 Electronics Avenue, 75008 Paris" },
      { id: 3, name: "Office Solutions", email: "<EMAIL>", phone: "01 45 67 89 10", address: "78 Office Boulevard, 75009 Paris" }
    ];
    const supplier = testSuppliers.find(s => s.id === parseInt(req.params.id));
    if (supplier) {
      res.json(supplier);
    } else {
      res.status(404).json({ error: 'Fournisseur non trouvé' });
    }
  }
});

// Route pour créer un nouveau fournisseur
app.post('/api/suppliers', async (req, res) => {
  try {
    const { name, contactPerson, email, phone, address, city, postalCode, country, taxId, notes } = req.body;

    console.log('🔍 Création d\'un nouveau fournisseur:', name);

    const result = await db.query(`
      INSERT INTO suppliers (name, contact_person, email, phone, address, city, postal_code, country, tax_id, notes)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [name, contactPerson, email, phone, address, city, postalCode, country, taxId, notes]);

    res.json({
      id: result.insertId,
      message: 'Fournisseur créé avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la création du fournisseur:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la création du fournisseur' });
  }
});

// Route pour mettre à jour un fournisseur
app.put('/api/suppliers/:id', async (req, res) => {
  try {
    const supplierId = req.params.id;
    const { name, contactPerson, email, phone, address, city, postalCode, country, taxId, notes } = req.body;

    console.log(`🔍 Mise à jour du fournisseur ${supplierId}:`, name);

    await db.query(`
      UPDATE suppliers
      SET name = ?, contact_person = ?, email = ?, phone = ?, address = ?, city = ?, postal_code = ?, country = ?, tax_id = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [name, contactPerson, email, phone, address, city, postalCode, country, taxId, notes, supplierId]);

    res.json({ message: 'Fournisseur mis à jour avec succès' });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du fournisseur:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la mise à jour du fournisseur' });
  }
});

// Route pour supprimer un fournisseur
app.delete('/api/suppliers/:id', async (req, res) => {
  try {
    const supplierId = req.params.id;

    console.log(`🔍 Suppression du fournisseur ${supplierId}...`);

    await db.query('DELETE FROM suppliers WHERE id = ?', [supplierId]);

    res.json({ message: 'Fournisseur supprimé avec succès' });
  } catch (error) {
    console.error('Erreur lors de la suppression du fournisseur:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la suppression du fournisseur' });
  }
});

app.get('/api/invoices', async (req, res) => {
  try {
    const invoices = await db.query(`
      SELECT i.*, c.name as clientName
      FROM invoices i
      LEFT JOIN clients c ON i.client_id = c.id
      ORDER BY i.id DESC
    `);

    // Transformer les données pour correspondre au format attendu par le frontend
    const formattedInvoices = invoices.map(invoice => ({
      id: invoice.id,
      invoiceNumber: invoice.invoice_number,
      date: invoice.date,
      dueDate: invoice.due_date,
      clientId: invoice.client_id,
      clientName: invoice.clientName || 'Client inconnu',
      subtotal: parseFloat(invoice.subtotal),
      taxRate: parseFloat(invoice.tax_rate),
      taxAmount: parseFloat(invoice.tax_amount),
      total: parseFloat(invoice.total),
      amountPaid: parseFloat(invoice.amount_paid),
      status: invoice.status,
      notes: invoice.notes
    }));

    res.json(formattedInvoices);
  } catch (error) {
    console.error('Erreur lors de la récupération des factures:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération des factures' });
  }
});

// Route pour récupérer une facture spécifique avec ses lignes de produits
app.get('/api/invoices/:id', async (req, res) => {
  try {
    const invoiceId = req.params.id;

    // Récupérer la facture avec les informations client
    const invoice = await db.queryOne(`
      SELECT i.*, c.name as clientName, c.email as clientEmail,
             c.phone as clientPhone, c.address as clientAddress
      FROM invoices i
      LEFT JOIN clients c ON i.client_id = c.id
      WHERE i.id = ?
    `, [invoiceId]);

    if (!invoice) {
      return res.status(404).json({ error: 'Facture non trouvée' });
    }

    // Récupérer les lignes de produits de la facture
    console.log(`🔍 Recherche des lignes de produits pour la facture ${invoiceId}...`);
    const items = await db.query(`
      SELECT ii.*, p.name as productName, p.description as productDescription
      FROM invoice_items ii
      LEFT JOIN products p ON ii.product_id = p.id
      WHERE ii.invoice_id = ?
      ORDER BY ii.id
    `, [invoiceId]);

    console.log(`📦 ${items.length} lignes de produits trouvées pour la facture ${invoiceId}`);

    // Transformer les données pour correspondre au format attendu par le frontend
    const formattedInvoice = {
      id: invoice.id,
      invoiceNumber: invoice.invoice_number,
      date: invoice.date,
      dueDate: invoice.due_date,
      clientId: invoice.client_id,
      clientName: invoice.clientName || 'Client inconnu',
      clientEmail: invoice.clientEmail,
      clientPhone: invoice.clientPhone,
      clientAddress: invoice.clientAddress,
      subtotal: parseFloat(invoice.subtotal),
      taxRate: parseFloat(invoice.tax_rate),
      taxAmount: parseFloat(invoice.tax_amount),
      total: parseFloat(invoice.total),
      amountPaid: parseFloat(invoice.amount_paid),
      status: invoice.status,
      notes: invoice.notes,
      items: items.map(item => ({
        id: item.id,
        productId: item.product_id,
        productName: item.productName,
        productDescription: item.productDescription,
        quantity: item.quantity,
        unitPrice: parseFloat(item.unit_price),
        discountPercent: parseFloat(item.discount_percent || 0),
        taxPercent: parseFloat(item.tax_percent || 19),
        total: parseFloat(item.total)
      }))
    };

    res.json(formattedInvoice);
  } catch (error) {
    console.error('Erreur lors de la récupération de la facture:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération de la facture' });
  }
});

// Route pour créer une nouvelle facture avec ses lignes de produits
app.post('/api/invoices', async (req, res) => {
  try {
    const { clientId, date, dueDate, subtotal, taxRate, taxAmount, total, notes, items } = req.body;

    // Générer un numéro de facture automatique
    const lastInvoice = await db.queryOne('SELECT invoice_number FROM invoices ORDER BY id DESC LIMIT 1');
    let invoiceNumber = 'FACT-001';

    if (lastInvoice) {
      const lastNumber = parseInt(lastInvoice.invoice_number.split('-')[1]);
      invoiceNumber = `FACT-${String(lastNumber + 1).padStart(3, '0')}`;
    }

    // Créer la facture
    const result = await db.query(`
      INSERT INTO invoices (invoice_number, client_id, date, due_date, subtotal, tax_rate, tax_amount, total, notes, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'unpaid')
    `, [invoiceNumber, clientId, date, dueDate, subtotal, taxRate, taxAmount, total, notes]);

    const invoiceId = result.insertId;

    // Ajouter les lignes de produits si elles existent
    if (items && items.length > 0) {
      for (const item of items) {
        await db.query(`
          INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, discount_percent, tax_percent, total)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [invoiceId, item.productId, item.quantity, item.unitPrice, item.discountPercent || 0, item.taxPercent || 19, item.total]);
      }
    }

    res.json({
      id: invoiceId,
      invoiceNumber,
      message: 'Facture créée avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la création de la facture:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la création de la facture' });
  }
});

// Route pour mettre à jour une facture avec ses lignes de produits
app.put('/api/invoices/:id', async (req, res) => {
  try {
    const invoiceId = req.params.id;
    const { clientId, date, dueDate, subtotal, taxRate, taxAmount, total, status, notes, items } = req.body;

    // Mettre à jour la facture
    await db.query(`
      UPDATE invoices
      SET client_id = ?, date = ?, due_date = ?, subtotal = ?, tax_rate = ?, tax_amount = ?, total = ?, status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [clientId, date, dueDate, subtotal, taxRate, taxAmount, total, status, notes, invoiceId]);

    // Supprimer les anciennes lignes de produits
    await db.query('DELETE FROM invoice_items WHERE invoice_id = ?', [invoiceId]);

    // Ajouter les nouvelles lignes de produits si elles existent
    if (items && items.length > 0) {
      for (const item of items) {
        await db.query(`
          INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, discount_percent, tax_percent, total)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [invoiceId, item.productId, item.quantity, item.unitPrice, item.discountPercent || 0, item.taxPercent || 19, item.total]);
      }
    }

    res.json({ message: 'Facture mise à jour avec succès' });
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la facture:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la mise à jour de la facture' });
  }
});

// Route pour supprimer une facture
app.delete('/api/invoices/:id', async (req, res) => {
  try {
    const invoiceId = req.params.id;

    await db.query('DELETE FROM invoices WHERE id = ?', [invoiceId]);

    res.json({ message: 'Facture supprimée avec succès' });
  } catch (error) {
    console.error('Erreur lors de la suppression de la facture:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la suppression de la facture' });
  }
});

// Route pour récupérer les catégories
app.get('/api/categories', async (req, res) => {
  try {
    const categories = await db.query('SELECT * FROM categories ORDER BY name');
    res.json(categories);
  } catch (error) {
    console.error('Erreur lors de la récupération des catégories:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération des catégories' });
  }
});

// Route pour récupérer un produit spécifique
app.get('/api/products/:id', async (req, res) => {
  try {
    const productId = req.params.id;
    console.log(`🔍 Récupération du produit ${productId}...`);

    const product = await db.queryOne(`
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.id = ?
    `, [productId]);

    if (!product) {
      return res.status(404).json({ error: 'Produit non trouvé' });
    }

    // Transformer les données pour correspondre au format attendu par le frontend
    const formattedProduct = {
      id: product.id,
      name: product.name,
      description: product.description,
      price: parseFloat(product.price),
      stockQuantity: product.stock_quantity,
      category: product.category || 'Sans catégorie',
      categoryId: product.category_id,
      barcode: product.barcode,
      sku: product.sku,
      minStockLevel: product.min_stock_level
    };

    res.json(formattedProduct);
  } catch (error) {
    console.error('❌ Erreur lors de la récupération du produit:', error.message);
    // Fallback avec des données de test
    const testProducts = [
      { id: 1, name: "Ordinateur portable", description: "PC portable haute performance", price: 89999.99, stockQuantity: 15, category: "Informatique", categoryId: 1, barcode: "123456789", sku: "ORD-001", minStockLevel: 5 },
      { id: 2, name: "Smartphone", description: "Téléphone intelligent", price: 49999.99, stockQuantity: 25, category: "Téléphonie", categoryId: 2, barcode: "987654321", sku: "TEL-001", minStockLevel: 10 },
      { id: 3, name: "Écran 24 pouces", description: "Moniteur LED 24 pouces", price: 19999.99, stockQuantity: 10, category: "Informatique", categoryId: 1, barcode: "*********", sku: "ECR-001", minStockLevel: 3 },
      { id: 4, name: "Souris sans fil", description: "Souris optique sans fil", price: 2999.99, stockQuantity: 50, category: "Accessoires", categoryId: 3, barcode: "*********", sku: "SOU-001", minStockLevel: 15 }
    ];
    const product = testProducts.find(p => p.id === parseInt(req.params.id));
    if (product) {
      res.json(product);
    } else {
      res.status(404).json({ error: 'Produit non trouvé' });
    }
  }
});

// Routes CRUD pour les produits
app.post('/api/products', async (req, res) => {
  try {
    const { name, description, price, stockQuantity, categoryId, barcode, sku, minStockLevel } = req.body;

    const result = await db.query(`
      INSERT INTO products (name, description, price, stock_quantity, category_id, barcode, sku, min_stock_level)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [name, description, price, stockQuantity, categoryId, barcode, sku, minStockLevel]);

    res.json({
      id: result.insertId,
      message: 'Produit créé avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la création du produit:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la création du produit' });
  }
});

app.put('/api/products/:id', async (req, res) => {
  try {
    const productId = req.params.id;
    const { name, description, price, stockQuantity, categoryId, barcode, sku, minStockLevel } = req.body;

    await db.query(`
      UPDATE products
      SET name = ?, description = ?, price = ?, stock_quantity = ?, category_id = ?, barcode = ?, sku = ?, min_stock_level = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [name, description, price, stockQuantity, categoryId, barcode, sku, minStockLevel, productId]);

    res.json({ message: 'Produit mis à jour avec succès' });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du produit:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la mise à jour du produit' });
  }
});

app.delete('/api/products/:id', async (req, res) => {
  try {
    const productId = req.params.id;

    await db.query('DELETE FROM products WHERE id = ?', [productId]);

    res.json({ message: 'Produit supprimé avec succès' });
  } catch (error) {
    console.error('Erreur lors de la suppression du produit:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la suppression du produit' });
  }
});

// Route pour récupérer un client spécifique
app.get('/api/clients/:id', async (req, res) => {
  try {
    const clientId = req.params.id;
    console.log(`🔍 Récupération du client ${clientId}...`);

    const client = await db.queryOne('SELECT * FROM clients WHERE id = ?', [clientId]);

    if (!client) {
      return res.status(404).json({ error: 'Client non trouvé' });
    }

    // Transformer les données pour correspondre au format attendu par le frontend
    const formattedClient = {
      id: client.id,
      name: client.name,
      contactPerson: client.contact_person,
      email: client.email,
      phone: client.phone,
      address: client.address,
      city: client.city,
      postalCode: client.postal_code,
      country: client.country,
      taxId: client.tax_id,
      notes: client.notes
    };

    res.json(formattedClient);
  } catch (error) {
    console.error('❌ Erreur lors de la récupération du client:', error.message);
    // Fallback avec des données de test
    const testClients = [
      { id: 1, name: "Entreprise ABC", contactPerson: "Jean Dupont", email: "<EMAIL>", phone: "021 23 45 67", address: "123 Rue du Commerce" },
      { id: 2, name: "Société XYZ", contactPerson: "Marie Martin", email: "<EMAIL>", phone: "041 98 76 54", address: "456 Avenue de la Liberté" },
      { id: 3, name: "Client Particulier", contactPerson: "Ahmed Ben Ali", email: "<EMAIL>", phone: "031 45 67 89", address: "789 Boulevard des Martyrs" }
    ];
    const client = testClients.find(c => c.id === parseInt(req.params.id));
    if (client) {
      res.json(client);
    } else {
      res.status(404).json({ error: 'Client non trouvé' });
    }
  }
});

// Routes CRUD pour les clients
app.post('/api/clients', async (req, res) => {
  try {
    const { name, contactPerson, email, phone, address, city, postalCode, country, taxId, notes } = req.body;

    const result = await db.query(`
      INSERT INTO clients (name, contact_person, email, phone, address, city, postal_code, country, tax_id, notes)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [name, contactPerson, email, phone, address, city, postalCode, country, taxId, notes]);

    res.json({
      id: result.insertId,
      message: 'Client créé avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la création du client:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la création du client' });
  }
});

app.put('/api/clients/:id', async (req, res) => {
  try {
    const clientId = req.params.id;
    const { name, contactPerson, email, phone, address, city, postalCode, country, taxId, notes } = req.body;

    await db.query(`
      UPDATE clients
      SET name = ?, contact_person = ?, email = ?, phone = ?, address = ?, city = ?, postal_code = ?, country = ?, tax_id = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [name, contactPerson, email, phone, address, city, postalCode, country, taxId, notes, clientId]);

    res.json({ message: 'Client mis à jour avec succès' });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du client:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la mise à jour du client' });
  }
});

app.delete('/api/clients/:id', async (req, res) => {
  try {
    const clientId = req.params.id;

    await db.query('DELETE FROM clients WHERE id = ?', [clientId]);

    res.json({ message: 'Client supprimé avec succès' });
  } catch (error) {
    console.error('Erreur lors de la suppression du client:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la suppression du client' });
  }
});

// ===== ROUTES SUPER ADMIN =====

// Route pour récupérer tous les utilisateurs
app.get('/api/users', async (req, res) => {
  try {
    console.log('🔍 Récupération de la liste des utilisateurs...');

    const users = await db.query(`
      SELECT id, username, email, role, full_name, phone,
             CASE WHEN is_active = 1 THEN 'active' ELSE 'inactive' END as status,
             last_login, created_at
      FROM users
      ORDER BY created_at DESC
    `);

    console.log(`✅ ${users.length} utilisateurs récupérés`);
    res.json(users);
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération des utilisateurs' });
  }
});

// Route pour créer un nouvel utilisateur
app.post('/api/users', async (req, res) => {
  try {
    const { username, email, password, role, fullName, phone, status } = req.body;

    console.log('📝 Création d\'un nouvel utilisateur:', username);

    // Vérifier si l'utilisateur existe déjà
    const existingUser = await db.queryOne(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    );

    if (existingUser) {
      return res.status(400).json({ error: 'Un utilisateur avec ce nom ou cet email existe déjà' });
    }

    // Hacher le mot de passe (simulation pour l'instant)
    const passwordHash = password; // Dans un vrai système, utiliser bcrypt

    const result = await db.query(`
      INSERT INTO users (username, email, password_hash, role, full_name, phone, is_active)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [username, email, passwordHash, role, fullName, phone, (status === 'active' ? 1 : 0)]);

    console.log('✅ Utilisateur créé avec l\'ID:', result.insertId);

    res.status(201).json({
      id: result.insertId,
      username,
      email,
      role,
      fullName,
      phone,
      status: status || 'active',
      message: 'Utilisateur créé avec succès'
    });
  } catch (error) {
    console.error('❌ Erreur lors de la création de l\'utilisateur:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la création de l\'utilisateur' });
  }
});

// Route pour modifier un utilisateur
app.put('/api/users/:id', async (req, res) => {
  try {
    const userId = req.params.id;
    const { username, email, password, role, fullName, phone, status } = req.body;

    console.log('📝 Modification de l\'utilisateur ID:', userId);

    // Vérifier si l'utilisateur existe
    const existingUser = await db.queryOne('SELECT id FROM users WHERE id = ?', [userId]);
    if (!existingUser) {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    // Construire la requête de mise à jour
    let updateQuery = `
      UPDATE users SET
        username = ?, email = ?, role = ?, full_name = ?, phone = ?, is_active = ?
    `;
    let params = [username, email, role, fullName, phone, (status === 'active' ? 1 : 0)];

    // Ajouter le mot de passe s'il est fourni
    if (password && password.trim() !== '') {
      updateQuery += ', password_hash = ?';
      params.push(password); // Dans un vrai système, hacher avec bcrypt
    }

    updateQuery += ' WHERE id = ?';
    params.push(userId);

    await db.query(updateQuery, params);

    console.log('✅ Utilisateur modifié avec succès');

    res.json({
      id: userId,
      username,
      email,
      role,
      fullName,
      phone,
      status,
      message: 'Utilisateur modifié avec succès'
    });
  } catch (error) {
    console.error('❌ Erreur lors de la modification de l\'utilisateur:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la modification de l\'utilisateur' });
  }
});

// Route pour supprimer un utilisateur
app.delete('/api/users/:id', async (req, res) => {
  try {
    const userId = req.params.id;

    console.log('🗑️ Suppression de l\'utilisateur ID:', userId);

    // Vérifier si l'utilisateur existe
    const existingUser = await db.queryOne('SELECT username FROM users WHERE id = ?', [userId]);
    if (!existingUser) {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    // Empêcher la suppression du dernier super admin
    const [superAdmins] = await db.query('SELECT COUNT(*) as count FROM users WHERE role = "superadmin"');
    const userToDelete = await db.queryOne('SELECT role FROM users WHERE id = ?', [userId]);

    if (userToDelete.role === 'superadmin' && superAdmins.count <= 1) {
      return res.status(400).json({ error: 'Impossible de supprimer le dernier super administrateur' });
    }

    await db.query('DELETE FROM users WHERE id = ?', [userId]);

    console.log('✅ Utilisateur supprimé:', existingUser.username);

    res.json({ message: 'Utilisateur supprimé avec succès' });
  } catch (error) {
    console.error('❌ Erreur lors de la suppression de l\'utilisateur:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la suppression de l\'utilisateur' });
  }
});

// Route pour réinitialiser le mot de passe d'un utilisateur
app.post('/api/users/:id/reset-password', async (req, res) => {
  try {
    const userId = req.params.id;

    console.log('🔑 Réinitialisation du mot de passe pour l\'utilisateur ID:', userId);

    // Vérifier si l'utilisateur existe
    const user = await db.queryOne('SELECT username FROM users WHERE id = ?', [userId]);
    if (!user) {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    // Générer un mot de passe temporaire
    const tempPassword = generateTempPassword();

    // Mettre à jour le mot de passe (dans un vrai système, hacher avec bcrypt)
    await db.query('UPDATE users SET password_hash = ? WHERE id = ?', [tempPassword, userId]);

    console.log('✅ Mot de passe réinitialisé pour:', user.username);

    res.json({
      message: 'Mot de passe réinitialisé avec succès',
      tempPassword: tempPassword,
      username: user.username
    });
  } catch (error) {
    console.error('❌ Erreur lors de la réinitialisation du mot de passe:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la réinitialisation du mot de passe' });
  }
});

// Fonction utilitaire pour générer un mot de passe temporaire
function generateTempPassword() {
  const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
  let password = '';
  for (let i = 0; i < 8; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

// Route d'authentification
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    console.log(`🔐 Tentative de connexion pour: ${username}`);

    if (!username || !password) {
      return res.status(400).json({ error: 'Nom d\'utilisateur et mot de passe requis' });
    }

    // Rechercher l'utilisateur dans la base de données
    const user = await db.queryOne('SELECT * FROM users WHERE username = ?', [username]);

    if (!user) {
      console.log(`❌ Utilisateur non trouvé: ${username}`);
      return res.status(401).json({ error: 'Nom d\'utilisateur ou mot de passe incorrect' });
    }

    // Vérifier le mot de passe (version simplifiée pour le développement)
    let passwordMatch = false;

    try {
      // Essayer d'importer bcrypt si disponible
      const bcrypt = require('bcrypt');
      passwordMatch = await bcrypt.compare(password, user.password_hash);
    } catch (error) {
      // Si bcrypt n'est pas disponible, comparer directement
      console.log('⚠️ bcrypt non disponible, comparaison directe du mot de passe');
      passwordMatch = (password === user.password_hash);
    }

    if (!passwordMatch) {
      console.log(`❌ Mot de passe incorrect pour: ${username}`);
      return res.status(401).json({ error: 'Nom d\'utilisateur ou mot de passe incorrect' });
    }

    // Vérifier si le compte est actif
    if (!user.is_active) {
      console.log(`❌ Compte inactif: ${username}`);
      return res.status(401).json({ error: 'Compte désactivé' });
    }

    // Mettre à jour la dernière connexion
    await db.query('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?', [user.id]);

    console.log(`✅ Connexion réussie pour: ${username} (${user.role})`);

    // Retourner les informations de l'utilisateur (sans le mot de passe)
    res.json({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      fullName: user.full_name,
      token: 'jwt-token-' + Math.random().toString(36).substring(2),
      message: 'Connexion réussie'
    });

  } catch (error) {
    console.error('❌ Erreur lors de l\'authentification:', error);
    res.status(500).json({ error: 'Erreur serveur lors de l\'authentification' });
  }
});

// Routes spécifiques
app.get('/', (req, res) => {
  // Rediriger vers la page de connexion par défaut
  res.redirect('/login.html');
});

app.get('/login.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'login.html'));
});

app.get('/index.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// Route par défaut pour servir les fichiers statiques ou rediriger
app.get('*', (req, res) => {
  // Si c'est un fichier statique, le middleware express.static s'en charge
  // Sinon, rediriger vers la page de connexion
  if (req.path.includes('.')) {
    // C'est probablement un fichier statique, laisser express.static gérer
    res.status(404).send('Fichier non trouvé');
  } else {
    // C'est une route, rediriger vers la page de connexion
    res.redirect('/login.html');
  }
});

// Démarrer le serveur sur toutes les interfaces réseau
app.listen(port, '0.0.0.0', () => {
  // Obtenir l'adresse IP locale
  const os = require('os');
  const networkInterfaces = os.networkInterfaces();
  let localIP = 'localhost';

  // Trouver l'adresse IP locale (non-loopback)
  for (const interfaceName in networkInterfaces) {
    const interfaces = networkInterfaces[interfaceName];
    for (const iface of interfaces) {
      if (iface.family === 'IPv4' && !iface.internal) {
        localIP = iface.address;
        break;
      }
    }
    if (localIP !== 'localhost') break;
  }

  console.log(`🚀 Serveur démarré avec succès !`);
  console.log(`📍 Accès local: http://localhost:${port}`);
  console.log(`🌐 Accès réseau: http://${localIP}:${port}`);
  console.log(`📱 Autres appareils du réseau peuvent accéder via: http://${localIP}:${port}`);
  console.log(`\n💡 Pour arrêter le serveur, appuyez sur Ctrl+C`);
});