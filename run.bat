@echo off
title GestiCom - Demarrage Automatique
color 0A

echo.
echo ========================================
echo    GESTICOM - DEMARRAGE AUTOMATIQUE
echo ========================================
echo.

echo [1/3] Verification de Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Node.js n'est pas installe
    echo Veuillez installer Node.js depuis https://nodejs.org/
    pause
    exit /b 1
)
echo ✓ Node.js OK

echo.
echo [2/3] Demarrage du serveur...
echo Serveur en cours de demarrage sur http://localhost:3000
echo.

REM Demarrer le serveur et ouvrir le navigateur
start /B node simple-server.js

REM Attendre 3 secondes
timeout /t 3 /nobreak >nul

echo [3/3] Ouverture de l'application...
start http://localhost:3000/login.html

echo.
echo ========================================
echo   GESTICOM EST MAINTENANT ACTIF !
echo ========================================
echo.
echo ✓ Serveur: http://localhost:3000
echo ✓ Application: http://localhost:3000/login.html
echo.
echo Comptes de demonstration:
echo • admin / admin (Administrateur)
echo • demo / demo (Demonstration)
echo.
echo Pour arreter le serveur, fermez cette fenetre
echo.

REM Garder la fenetre ouverte
pause
