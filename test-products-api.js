// Test spécifique de l'API des produits
const http = require('http');

function testAPI(path, description) {
  return new Promise((resolve) => {
    console.log(`🔍 Test: ${description}`);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          console.log(`✅ ${description} - Status: ${res.statusCode}`);
          console.log(`📊 Données reçues:`, jsonData);
          resolve({ success: true, data: jsonData, status: res.statusCode });
        } catch (error) {
          console.log(`❌ ${description} - Erreur JSON:`, error.message);
          console.log(`📄 Réponse brute:`, data);
          resolve({ success: false, error: error.message, status: res.statusCode });
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ ${description} - <PERSON><PERSON><PERSON> r<PERSON>eau:`, error.message);
      resolve({ success: false, error: error.message });
    });

    req.end();
  });
}

async function testProductsAPI() {
  console.log('🧪 Test de l\'API des produits\n');

  // Test 1: Liste des produits
  const productsTest = await testAPI('/api/products', 'Liste des produits');
  
  if (productsTest.success && productsTest.data.length > 0) {
    console.log(`\n📦 ${productsTest.data.length} produits trouvés:`);
    productsTest.data.forEach((product, index) => {
      const stock = product.stockQuantity || product.stock_quantity || 'N/A';
      console.log(`  ${index + 1}. ${product.name} - ${product.price}€ (Stock: ${stock})`);
    });

    // Test 2: Récupération d'un produit spécifique
    const firstProductId = productsTest.data[0].id;
    console.log(`\n🔍 Test de récupération du produit ID ${firstProductId}:`);
    const productTest = await testAPI(`/api/products/${firstProductId}`, `Produit ${firstProductId}`);
    
    if (productTest.success) {
      console.log(`✅ Produit récupéré: ${productTest.data.name}`);
    }
  }

  // Test 3: Statut du serveur
  console.log('\n📊 Test du statut du serveur:');
  const statusTest = await testAPI('/api/status', 'Statut du serveur');
  
  if (statusTest.success) {
    console.log(`✅ MySQL: ${statusTest.data.mysql ? 'Connecté' : 'Non connecté'}`);
    console.log(`✅ Source des données: ${statusTest.data.dataSource || 'Non spécifiée'}`);
  }

  console.log('\n🎯 Résumé des tests:');
  console.log(`- API Produits: ${productsTest.success ? '✅ OK' : '❌ Erreur'}`);
  console.log(`- API Statut: ${statusTest.success ? '✅ OK' : '❌ Erreur'}`);
  
  if (productsTest.success) {
    console.log('\n💡 L\'API fonctionne correctement. Si la page produits ne se charge pas,');
    console.log('   le problème vient probablement du JavaScript côté client.');
    console.log('\n🔧 Solutions possibles:');
    console.log('1. Vérifiez la console du navigateur (F12)');
    console.log('2. Vérifiez que products.js est bien chargé');
    console.log('3. Vérifiez la fonction loadProductsList()');
  } else {
    console.log('\n❌ L\'API ne fonctionne pas correctement.');
    console.log('🔧 Vérifiez la connexion MySQL et les logs du serveur.');
  }
}

testProductsAPI();
