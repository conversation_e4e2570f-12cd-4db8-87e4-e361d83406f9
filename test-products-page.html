<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page Produits</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test de la Page Produits</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test API Direct</h3>
                <button id="testAPI" class="btn btn-primary">Tester API /api/products</button>
                <div id="apiResult" class="mt-3"></div>
            </div>
            
            <div class="col-md-6">
                <h3>Test avec products.js</h3>
                <button id="testProductsJS" class="btn btn-success">Tester loadProductsList()</button>
                <div id="productsJSResult" class="mt-3"></div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>Tableau des Produits</h3>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nom</th>
                            <th>Prix</th>
                            <th>Stock</th>
                            <th>Catégorie</th>
                        </tr>
                    </thead>
                    <tbody id="productsTable">
                        <tr>
                            <td colspan="5" class="text-center">Aucun produit chargé</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Test API direct
        document.getElementById('testAPI').addEventListener('click', async function() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm"></div> Chargement...';
            
            try {
                const response = await fetch('/api/products');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <strong>Succès!</strong> ${data.length} produits récupérés
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
                
                // Remplir le tableau
                fillProductsTable(data);
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Erreur!</strong> ${error.message}
                    </div>
                `;
            }
        });

        // Test avec products.js (si disponible)
        document.getElementById('testProductsJS').addEventListener('click', function() {
            const resultDiv = document.getElementById('productsJSResult');
            
            if (typeof loadProductsList === 'function') {
                resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm"></div> Test loadProductsList()...';
                try {
                    loadProductsList();
                    resultDiv.innerHTML = '<div class="alert alert-success">loadProductsList() exécutée</div>';
                } catch (error) {
                    resultDiv.innerHTML = `<div class="alert alert-danger">Erreur: ${error.message}</div>`;
                }
            } else {
                resultDiv.innerHTML = '<div class="alert alert-warning">loadProductsList() non disponible</div>';
            }
        });

        // Fonction pour remplir le tableau
        function fillProductsTable(products) {
            const tableBody = document.getElementById('productsTable');
            tableBody.innerHTML = '';
            
            products.forEach(product => {
                const stockQuantity = product.stockQuantity || product.stock_quantity || 0;
                const categoryName = product.category || product.category_name || product.category_id || '-';
                
                tableBody.innerHTML += `
                    <tr>
                        <td>${product.id}</td>
                        <td>${product.name}</td>
                        <td>${parseFloat(product.price).toFixed(2)} DA</td>
                        <td>${stockQuantity}</td>
                        <td>${categoryName}</td>
                    </tr>
                `;
            });
        }

        // Test automatique au chargement
        window.addEventListener('load', function() {
            console.log('🧪 Page de test chargée');
            console.log('🔍 Test automatique de l\'API...');
            document.getElementById('testAPI').click();
        });
    </script>

    <!-- Charger products.js si disponible -->
    <script src="js/products.js" onerror="console.log('products.js non trouvé')"></script>
</body>
</html>
