// Script pour vérifier la structure des tables
const mysql = require('mysql2/promise');

async function checkStructure() {
  try {
    console.log('🔍 Vérification de la structure des tables...\n');
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'gesticom',
      port: 3306
    });

    const tables = ['products', 'clients', 'suppliers', 'invoices', 'users'];
    
    for (const tableName of tables) {
      try {
        console.log(`📋 Structure de la table "${tableName}":`);
        const [columns] = await connection.execute(`DESCRIBE ${tableName}`);
        columns.forEach(col => {
          console.log(`  - ${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key ? col.Key : ''}`);
        });
        console.log('');
      } catch (e) {
        console.log(`❌ Table "${tableName}" non trouvée\n`);
      }
    }

    await connection.end();
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

checkStructure();
