const inventory = {
  checkStock: async (productId) => {
    const product = await productManager.getById(productId);
    return product.stockQuantity;
  },
  alertLowStock: () => {
    // Alerter pour les produits en rupture de stock
    const lowStockProducts = products.filter(product => product.stockQuantity < 10);
    return lowStockProducts;
  },
  updateAfterSale: async (items) => {
    // Mettre à jour l'inventaire après une vente
    for (const item of items) {
      const product = await productManager.getById(item.productId);
      const newQuantity = product.stockQuantity - item.quantity;
      await productManager.updateStock(item.productId, newQuantity >= 0 ? newQuantity : 0);
    }
  },
  adjustStock: async (productId, newQuantity) => {
    try {
      return await productManager.updateStock(productId, newQuantity);
    } catch (error) {
      console.error('Erreur lors de l\'ajustement du stock:', error);
      throw error;
    }
  }
};

// Fonction d'initialisation pour la section inventaire
function initInventorySection() {
  // Charger les données d'inventaire
  loadInventoryData();
  
  // Ajouter les gestionnaires d'événements
}

// Fonction pour charger les données d'inventaire
async function loadInventoryData() {
  try {
    // Charger les données d'inventaire
    // À implémenter
  } catch (error) {
    console.error('Erreur lors du chargement de l\'inventaire:', error);
  }
}


