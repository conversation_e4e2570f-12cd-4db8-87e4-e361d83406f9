#!/usr/bin/env node

// Script de démarrage avec initialisation automatique de la base de données
const { spawn, exec } = require('child_process');
const mysql = require('mysql2/promise');
const path = require('path');

console.log('\n========================================');
console.log('  GESTICOM - DÉMARRAGE AVEC BASE DE DONNÉES');
console.log('========================================\n');

// Configuration
const PORT = 3000;
const SERVER_FILE = 'simple-server.js';
const APP_URL = `http://localhost:${PORT}/login.html`;

const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '', // Modifiez selon votre configuration MySQL
  port: 3306
};

// Fonction pour vérifier si MySQL est disponible
async function checkMySQL() {
  try {
    console.log('🔍 Vérification de MySQL...');
    const connection = await mysql.createConnection(dbConfig);
    await connection.ping();
    await connection.end();
    console.log('✅ MySQL est disponible');
    return true;
  } catch (error) {
    console.log('❌ MySQL non disponible:', error.message);
    return false;
  }
}

// Fonction pour vérifier si la base de données existe
async function checkDatabase() {
  try {
    const connection = await mysql.createConnection(dbConfig);
    const [databases] = await connection.execute("SHOW DATABASES LIKE 'gesticom'");
    await connection.end();
    return databases.length > 0;
  } catch (error) {
    return false;
  }
}

// Fonction pour initialiser la base de données
async function initializeDatabase() {
  return new Promise((resolve, reject) => {
    console.log('🗄️ Initialisation de la base de données...');
    
    const setupProcess = spawn('node', ['setup-database.js'], {
      stdio: 'inherit'
    });

    setupProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Base de données initialisée avec succès');
        resolve();
      } else {
        console.log('❌ Erreur lors de l\'initialisation de la base de données');
        reject(new Error(`Setup failed with code ${code}`));
      }
    });

    setupProcess.on('error', (error) => {
      console.log('❌ Erreur lors du lancement du setup:', error.message);
      reject(error);
    });
  });
}

// Fonction pour ouvrir l'URL dans le navigateur
function openBrowser(url) {
  const platform = process.platform;
  let command;

  switch (platform) {
    case 'win32':
      command = `start ${url}`;
      break;
    case 'darwin':
      command = `open ${url}`;
      break;
    case 'linux':
      command = `xdg-open ${url}`;
      break;
    default:
      console.log(`⚠️ Impossible d'ouvrir automatiquement le navigateur sur ${platform}`);
      console.log(`🌐 Ouvrez manuellement: ${url}`);
      return;
  }

  exec(command, (error) => {
    if (error) {
      console.log(`⚠️ Erreur lors de l'ouverture du navigateur: ${error.message}`);
      console.log(`🌐 Ouvrez manuellement: ${url}`);
    } else {
      console.log('✅ Application ouverte dans le navigateur');
    }
  });
}

// Fonction pour vérifier si le serveur répond
function checkServer(callback) {
  const http = require('http');
  
  const options = {
    hostname: 'localhost',
    port: PORT,
    path: '/api/status',
    method: 'GET',
    timeout: 2000
  };

  const req = http.request(options, (res) => {
    callback(res.statusCode === 200);
  });

  req.on('error', () => {
    callback(false);
  });

  req.on('timeout', () => {
    req.destroy();
    callback(false);
  });

  req.end();
}

// Fonction principale
async function startApplication() {
  try {
    console.log('🔍 Vérification de Node.js...');
    
    // Vérifier si le fichier serveur existe
    const fs = require('fs');
    if (!fs.existsSync(SERVER_FILE)) {
      console.log(`❌ Fichier ${SERVER_FILE} non trouvé`);
      console.log(`📁 Répertoire courant: ${process.cwd()}`);
      process.exit(1);
    }

    console.log('✅ Fichiers de l\'application trouvés');

    // Vérifier MySQL
    const mysqlAvailable = await checkMySQL();
    
    if (mysqlAvailable) {
      // Vérifier si la base de données existe
      const dbExists = await checkDatabase();
      
      if (!dbExists) {
        console.log('🗄️ Base de données non trouvée, initialisation...');
        await initializeDatabase();
      } else {
        console.log('✅ Base de données "gesticom" trouvée');
      }
    } else {
      console.log('⚠️ MySQL non disponible - l\'application utilisera des données de fallback');
      console.log('💡 Pour utiliser MySQL, installez-le et configurez les paramètres dans config/mysql.js');
    }

    console.log('🚀 Démarrage du serveur GestiCom...');

    // Démarrer le serveur
    const server = spawn('node', [SERVER_FILE], {
      stdio: ['inherit', 'pipe', 'pipe'],
      cwd: process.cwd()
    });

    // Gérer la sortie du serveur
    server.stdout.on('data', (data) => {
      process.stdout.write(data);
    });

    server.stderr.on('data', (data) => {
      process.stderr.write(data);
    });

    // Gérer l'arrêt du serveur
    server.on('close', (code) => {
      console.log(`\n🛑 Serveur arrêté avec le code: ${code}`);
      process.exit(code);
    });

    server.on('error', (error) => {
      console.log(`❌ Erreur lors du démarrage du serveur: ${error.message}`);
      process.exit(1);
    });

    // Attendre que le serveur soit prêt
    console.log('⏳ Attente du démarrage du serveur...');
    
    let attempts = 0;
    const maxAttempts = 15;
    
    const checkInterval = setInterval(() => {
      attempts++;
      
      checkServer((isReady) => {
        if (isReady) {
          clearInterval(checkInterval);
          console.log('✅ Serveur démarré avec succès!');
          console.log('\n========================================');
          console.log('   GESTICOM EST MAINTENANT ACTIF !');
          console.log('========================================\n');
          console.log(`🌐 URL: ${APP_URL}`);
          console.log(`📊 API Status: http://localhost:${PORT}/api/status`);
          console.log(`🗄️ Base de données: ${mysqlAvailable ? 'MySQL' : 'Données de fallback'}`);
          console.log('\n🔐 Comptes de démonstration:');
          console.log('• admin / admin (Administrateur)');
          console.log('• demo / demo (Démonstration)');
          console.log('\n💡 Pour arrêter le serveur, appuyez sur Ctrl+C\n');
          
          // Ouvrir le navigateur après un délai
          setTimeout(() => {
            openBrowser(APP_URL);
          }, 1000);
          
        } else if (attempts >= maxAttempts) {
          clearInterval(checkInterval);
          console.log(`❌ Échec du démarrage du serveur après ${maxAttempts} tentatives`);
          console.log(`🌐 Essayez d'ouvrir manuellement: ${APP_URL}`);
        } else if (attempts % 3 === 0) {
          console.log(`⏳ Tentative ${attempts}/${maxAttempts}...`);
        }
      });
    }, 1000);

    // Gérer l'arrêt propre
    process.on('SIGINT', () => {
      console.log('\n🛑 Arrêt du serveur...');
      server.kill('SIGINT');
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 Arrêt du serveur...');
      server.kill('SIGTERM');
    });

  } catch (error) {
    console.log(`❌ Erreur: ${error.message}`);
    process.exit(1);
  }
}

// Démarrer l'application
startApplication();
