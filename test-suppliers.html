<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fournisseurs - GestiCom</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test de la Page Fournisseurs</h1>
        
        <div class="alert alert-info">
            <strong>Test de la section fournisseurs avec les corrections appliquées</strong>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Tests API Fournisseurs</h3>
                <div class="d-grid gap-2">
                    <button id="testGetSuppliers" class="btn btn-primary">Test GET /api/suppliers</button>
                    <button id="testGetSupplier" class="btn btn-primary">Test GET /api/suppliers/1</button>
                    <button id="testCreateSupplier" class="btn btn-success">Test POST /api/suppliers</button>
                    <button id="testUpdateSupplier" class="btn btn-warning">Test PUT /api/suppliers/1</button>
                    <button id="testDeleteSupplier" class="btn btn-danger">Test DELETE /api/suppliers/999</button>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>Tests Interface</h3>
                <div class="d-grid gap-2">
                    <button id="testSupplierManager" class="btn btn-info">Test SupplierManager</button>
                    <button id="testInitFunction" class="btn btn-info">Test initSuppliersSection()</button>
                    <button id="testLoadSuppliers" class="btn btn-info">Test loadSuppliersList()</button>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Tableau des Fournisseurs</h3>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom</th>
                        <th>Email</th>
                        <th>Téléphone</th>
                        <th>Adresse</th>
                    </tr>
                </thead>
                <tbody id="suppliersTable">
                    <tr>
                        <td colspan="5" class="text-center">Aucun fournisseur chargé</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="mt-4">
            <h3>Résultats des Tests</h3>
            <div id="testResults" class="border p-3" style="height: 300px; overflow-y: auto; background-color: #f8f9fa;">
                <div class="text-muted">Les résultats des tests apparaîtront ici...</div>
            </div>
        </div>
        
        <div class="mt-3">
            <button id="runAllTests" class="btn btn-success btn-lg">Exécuter Tous les Tests</button>
            <button id="clearResults" class="btn btn-outline-secondary">Effacer les Résultats</button>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Scripts dans le bon ordre -->
    <script src="js/utils.js"></script>
    <script src="js/suppliers.js"></script>

    <script>
        const resultsDiv = document.getElementById('testResults');
        
        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const alertClass = type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info';
            resultsDiv.innerHTML += `
                <div class="alert alert-${alertClass} py-2 mb-2">
                    <small>[${timestamp}]</small> ${message}
                </div>
            `;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Test GET /api/suppliers
        document.getElementById('testGetSuppliers').addEventListener('click', async () => {
            addResult('🧪 Test GET /api/suppliers...', 'info');
            try {
                const response = await fetch('/api/suppliers');
                const data = await response.json();
                addResult(`✅ Succès: ${data.length} fournisseurs récupérés`, 'success');
                
                // Remplir le tableau
                fillSuppliersTable(data);
                
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Test GET /api/suppliers/:id
        document.getElementById('testGetSupplier').addEventListener('click', async () => {
            addResult('🧪 Test GET /api/suppliers/1...', 'info');
            try {
                const response = await fetch('/api/suppliers/1');
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Succès: Fournisseur "${data.name}" récupéré`, 'success');
                } else {
                    addResult(`⚠️ Fournisseur non trouvé (${response.status})`, 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Test POST /api/suppliers
        document.getElementById('testCreateSupplier').addEventListener('click', async () => {
            addResult('🧪 Test POST /api/suppliers...', 'info');
            try {
                const testSupplier = {
                    name: 'Fournisseur Test',
                    email: '<EMAIL>',
                    phone: '01 23 45 67 89',
                    address: '123 Rue Test'
                };
                
                const response = await fetch('/api/suppliers', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testSupplier)
                });
                
                const data = await response.json();
                if (response.ok) {
                    addResult(`✅ Succès: Fournisseur créé avec ID ${data.id}`, 'success');
                } else {
                    addResult(`❌ Erreur création: ${data.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Test PUT /api/suppliers/:id
        document.getElementById('testUpdateSupplier').addEventListener('click', async () => {
            addResult('🧪 Test PUT /api/suppliers/1...', 'info');
            try {
                const updateData = {
                    name: 'Fournisseur Modifié',
                    email: '<EMAIL>'
                };
                
                const response = await fetch('/api/suppliers/1', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(updateData)
                });
                
                const data = await response.json();
                if (response.ok) {
                    addResult(`✅ Succès: Fournisseur mis à jour`, 'success');
                } else {
                    addResult(`❌ Erreur mise à jour: ${data.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Test DELETE /api/suppliers/:id
        document.getElementById('testDeleteSupplier').addEventListener('click', async () => {
            addResult('🧪 Test DELETE /api/suppliers/999...', 'info');
            try {
                const response = await fetch('/api/suppliers/999', {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                if (response.ok) {
                    addResult(`✅ Succès: Fournisseur supprimé`, 'success');
                } else {
                    addResult(`⚠️ Fournisseur non trouvé (normal pour test)`, 'info');
                }
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Test SupplierManager
        document.getElementById('testSupplierManager').addEventListener('click', async () => {
            addResult('🧪 Test SupplierManager...', 'info');
            try {
                if (typeof supplierManager !== 'undefined') {
                    addResult('✅ SupplierManager disponible', 'success');
                    const suppliers = await supplierManager.getAll();
                    addResult(`✅ SupplierManager.getAll(): ${suppliers.length} fournisseurs`, 'success');
                } else {
                    addResult('❌ SupplierManager non disponible', 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur SupplierManager: ${error.message}`, 'error');
            }
        });
        
        // Test initSuppliersSection
        document.getElementById('testInitFunction').addEventListener('click', () => {
            addResult('🧪 Test initSuppliersSection()...', 'info');
            try {
                if (typeof initSuppliersSection !== 'undefined') {
                    addResult('✅ initSuppliersSection() disponible', 'success');
                } else {
                    addResult('❌ initSuppliersSection() non disponible', 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Test loadSuppliersList
        document.getElementById('testLoadSuppliers').addEventListener('click', () => {
            addResult('🧪 Test loadSuppliersList()...', 'info');
            try {
                if (typeof loadSuppliersList !== 'undefined') {
                    addResult('✅ loadSuppliersList() disponible', 'success');
                } else {
                    addResult('❌ loadSuppliersList() non disponible', 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
            }
        });
        
        // Fonction pour remplir le tableau
        function fillSuppliersTable(suppliers) {
            const tableBody = document.getElementById('suppliersTable');
            tableBody.innerHTML = '';
            
            suppliers.forEach(supplier => {
                tableBody.innerHTML += `
                    <tr>
                        <td>${supplier.id}</td>
                        <td>${supplier.name}</td>
                        <td>${supplier.email || '-'}</td>
                        <td>${supplier.phone || '-'}</td>
                        <td>${supplier.address || '-'}</td>
                    </tr>
                `;
            });
        }
        
        // Exécuter tous les tests
        document.getElementById('runAllTests').addEventListener('click', async () => {
            addResult('🚀 Exécution de tous les tests...', 'info');
            
            const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
            
            document.getElementById('testGetSuppliers').click();
            await delay(500);
            document.getElementById('testGetSupplier').click();
            await delay(500);
            document.getElementById('testSupplierManager').click();
            await delay(500);
            document.getElementById('testInitFunction').click();
            await delay(500);
            document.getElementById('testCreateSupplier').click();
            await delay(500);
            document.getElementById('testUpdateSupplier').click();
            await delay(500);
            document.getElementById('testDeleteSupplier').click();
            
            addResult('🎉 Tous les tests terminés !', 'success');
        });
        
        // Effacer les résultats
        document.getElementById('clearResults').addEventListener('click', () => {
            resultsDiv.innerHTML = '<div class="text-muted">Résultats effacés...</div>';
        });
        
        // Test automatique au chargement
        window.addEventListener('load', () => {
            addResult('🚀 Page de test fournisseurs chargée', 'info');
            addResult('💡 Cliquez sur "Exécuter Tous les Tests" pour commencer', 'info');
        });
    </script>
</body>
</html>
